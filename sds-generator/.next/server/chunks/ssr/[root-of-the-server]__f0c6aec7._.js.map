{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/experiment/SDS%20Augmented/sds-generator/src/types/sds.ts"], "sourcesContent": ["// types/sds.ts\n\nexport interface ProductDetails {\n  gasName: string;\n  casNumber: string;\n  unNumber: string;\n  // This is key for logic: 'Compressed', 'Liquefied', 'Refrigerated', 'Dissolved'\n  gasCategory: 'Compressed' | 'Liquefied' | 'Refrigerated' | 'Dissolved' | '';\n  recommendedUse: string;\n  supplierName: string;\n  supplierAddress: string;\n  supplierPhone: string;\n  emergencyPhone: string;\n  isFlammable?: boolean; // Optional for more complex gases\n  isToxic?: boolean; // Optional\n  molecularFormula?: string;\n  molecularWeight?: string;\n  appearance?: string;\n  odor?: string;\n}\n\nexport interface SDSSection {\n  en: string;\n  my: string;\n}\n\n// You can expand this to hold the generated content for all 16 sections\nexport interface GeneratedSDS {\n  section1: SDSSection; // Identification\n  section2: SDSSection; // Hazard identification\n  section3: SDSSection; // Composition/information on ingredients\n  section4: SDSSection; // First aid measures\n  section5: SDSSection; // Fire-fighting measures\n  section6: SDSSection; // Accidental release measures\n  section7: SDSSection; // Handling and storage\n  section8: SDSSection; // Exposure controls/personal protection\n  section9: SDSSection; // Physical and chemical properties\n  section10: SDSSection; // Stability and reactivity\n  section11: SDSSection; // Toxicological information\n  section12: SDSSection; // Ecological information\n  section13: SDSSection; // Disposal considerations\n  section14: SDSSection; // Transport information\n  section15: SDSSection; // Regulatory information\n  section16: SDSSection; // Other information\n}\n\nexport const GAS_CATEGORIES = [\n  { value: 'Compressed', label: 'Compressed Gas' },\n  { value: 'Liquefied', label: 'Liquefied Gas' },\n  { value: 'Refrigerated', label: 'Refrigerated Liquefied Gas' },\n  { value: 'Dissolved', label: 'Dissolved Gas' },\n] as const;\n\nexport const SECTION_TITLES = {\n  section1: { en: 'Identification', my: 'Pengenalan' },\n  section2: { en: 'Hazard Identification', my: 'Pengenalan Bahaya' },\n  section3: { en: 'Composition/Information on Ingredients', my: 'Komposisi/Maklumat mengenai Ramuan' },\n  section4: { en: 'First Aid Measures', my: 'Langkah-langkah Pertolongan Cemas' },\n  section5: { en: 'Fire-fighting Measures', my: 'Langkah-langkah Memadam Api' },\n  section6: { en: 'Accidental Release Measures', my: 'Langkah-langkah Pelepasan Tidak Sengaja' },\n  section7: { en: 'Handling and Storage', my: 'Pengendalian dan Penyimpanan' },\n  section8: { en: 'Exposure Controls/Personal Protection', my: 'Kawalan Pendedahan/Perlindungan Peribadi' },\n  section9: { en: 'Physical and Chemical Properties', my: 'Sifat-sifat Fizikal dan Kimia' },\n  section10: { en: 'Stability and Reactivity', my: 'Kestabilan dan Kereaktifan' },\n  section11: { en: 'Toxicological Information', my: 'Maklumat Toksikologi' },\n  section12: { en: 'Ecological Information', my: 'Maklumat Ekologi' },\n  section13: { en: 'Disposal Considerations', my: 'Pertimbangan Pelupusan' },\n  section14: { en: 'Transport Information', my: 'Maklumat Pengangkutan' },\n  section15: { en: 'Regulatory Information', my: 'Maklumat Peraturan' },\n  section16: { en: 'Other Information', my: 'Maklumat Lain' },\n} as const;\n"], "names": [], "mappings": "AAAA,eAAe;;;;;AA8CR,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAc,OAAO;IAAiB;IAC/C;QAAE,OAAO;QAAa,OAAO;IAAgB;IAC7C;QAAE,OAAO;QAAgB,OAAO;IAA6B;IAC7D;QAAE,OAAO;QAAa,OAAO;IAAgB;CAC9C;AAEM,MAAM,iBAAiB;IAC5B,UAAU;QAAE,IAAI;QAAkB,IAAI;IAAa;IACnD,UAAU;QAAE,IAAI;QAAyB,IAAI;IAAoB;IACjE,UAAU;QAAE,IAAI;QAA0C,IAAI;IAAqC;IACnG,UAAU;QAAE,IAAI;QAAsB,IAAI;IAAoC;IAC9E,UAAU;QAAE,IAAI;QAA0B,IAAI;IAA8B;IAC5E,UAAU;QAAE,IAAI;QAA+B,IAAI;IAA0C;IAC7F,UAAU;QAAE,IAAI;QAAwB,IAAI;IAA+B;IAC3E,UAAU;QAAE,IAAI;QAAyC,IAAI;IAA2C;IACxG,UAAU;QAAE,IAAI;QAAoC,IAAI;IAAgC;IACxF,WAAW;QAAE,IAAI;QAA4B,IAAI;IAA6B;IAC9E,WAAW;QAAE,IAAI;QAA6B,IAAI;IAAuB;IACzE,WAAW;QAAE,IAAI;QAA0B,IAAI;IAAmB;IAClE,WAAW;QAAE,IAAI;QAA2B,IAAI;IAAyB;IACzE,WAAW;QAAE,IAAI;QAAyB,IAAI;IAAwB;IACtE,WAAW;QAAE,IAAI;QAA0B,IAAI;IAAqB;IACpE,WAAW;QAAE,IAAI;QAAqB,IAAI;IAAgB;AAC5D", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/experiment/SDS%20Augmented/sds-generator/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/experiment/SDS%20Augmented/sds-generator/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/experiment/SDS%20Augmented/sds-generator/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/experiment/SDS%20Augmented/sds-generator/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/experiment/SDS%20Augmented/sds-generator/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/experiment/SDS%20Augmented/sds-generator/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/experiment/SDS%20Augmented/sds-generator/src/components/SDSForm.tsx"], "sourcesContent": ["// components/SDSForm.tsx\n\"use client\";\n\nimport { useState } from 'react';\nimport { ProductDetails, GAS_CATEGORIES } from '@/types/sds';\nimport { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Button } from '@/components/ui/button';\n\ninterface SDSFormProps {\n  onDataChange: (data: ProductDetails) => void;\n}\n\nexport function SDSForm({ onDataChange }: SDSFormProps) {\n  const [details, setDetails] = useState<ProductDetails>({\n    gasName: '',\n    casNumber: '',\n    unNumber: '',\n    gasCategory: '',\n    recommendedUse: '',\n    supplierName: '',\n    supplierAddress: '',\n    supplierPhone: '',\n    emergencyPhone: '',\n    molecularFormula: '',\n    molecularWeight: '',\n    appearance: '',\n    odor: '',\n  });\n\n  const handleChange = (field: keyof ProductDetails, value: string) => {\n    const newDetails = { ...details, [field]: value };\n    setDetails(newDetails);\n    onDataChange(newDetails);\n  };\n\n  const handleClear = () => {\n    const emptyDetails: ProductDetails = {\n      gasName: '',\n      casNumber: '',\n      unNumber: '',\n      gasCategory: '',\n      recommendedUse: '',\n      supplierName: '',\n      supplierAddress: '',\n      supplierPhone: '',\n      emergencyPhone: '',\n      molecularFormula: '',\n      molecularWeight: '',\n      appearance: '',\n      odor: '',\n    };\n    setDetails(emptyDetails);\n    onDataChange(emptyDetails);\n  };\n\n  const handleLoadSample = () => {\n    const sampleDetails: ProductDetails = {\n      gasName: 'Hydrogen',\n      casNumber: '1333-74-0',\n      unNumber: 'UN1049',\n      gasCategory: 'Compressed',\n      recommendedUse: 'Industrial gas, Laboratory use, Fuel cell applications',\n      supplierName: 'Alpha Gas Solution Sdn Bhd',\n      supplierAddress: 'No. 123, Jalan Industri 4/5, Kawasan Perindustrian Bandar Baru Bangi, 43650 Bandar Baru Bangi, Selangor',\n      supplierPhone: '+60 3-8925 1234',\n      emergencyPhone: '+60 3-8925 5678',\n      molecularFormula: 'H₂',\n      molecularWeight: '2.016 g/mol',\n      appearance: 'Colorless gas',\n      odor: 'Odorless',\n    };\n    setDetails(sampleDetails);\n    onDataChange(sampleDetails);\n  };\n\n  const isFormValid = details.gasName && details.gasCategory && details.casNumber && details.unNumber;\n\n  return (\n    <Card className=\"w-full\">\n      <CardHeader>\n        <CardTitle className=\"text-2xl font-bold text-center\">SDS Generator</CardTitle>\n        <p className=\"text-sm text-muted-foreground text-center\">\n          Generate Safety Data Sheets following ICOP 2014 guidelines\n        </p>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Product Information Section */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold border-b pb-2\">Product Information</h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <Label htmlFor=\"gasName\">Gas Name *</Label>\n              <Input \n                id=\"gasName\" \n                placeholder=\"e.g., Nitrogen, Oxygen, Argon\"\n                value={details.gasName} \n                onChange={(e) => handleChange('gasName', e.target.value)} \n              />\n            </div>\n\n            <div>\n              <Label htmlFor=\"gasCategory\">Gas Category *</Label>\n              <Select onValueChange={(value) => handleChange('gasCategory', value)} value={details.gasCategory}>\n                <SelectTrigger id=\"gasCategory\">\n                  <SelectValue placeholder=\"Select gas category...\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {GAS_CATEGORIES.map((category) => (\n                    <SelectItem key={category.value} value={category.value}>\n                      {category.label}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div>\n              <Label htmlFor=\"casNumber\">CAS Number *</Label>\n              <Input \n                id=\"casNumber\" \n                placeholder=\"e.g., 7727-37-9\"\n                value={details.casNumber} \n                onChange={(e) => handleChange('casNumber', e.target.value)} \n              />\n            </div>\n\n            <div>\n              <Label htmlFor=\"unNumber\">UN Number *</Label>\n              <Input \n                id=\"unNumber\" \n                placeholder=\"e.g., UN1066\"\n                value={details.unNumber} \n                onChange={(e) => handleChange('unNumber', e.target.value)} \n              />\n            </div>\n\n            <div>\n              <Label htmlFor=\"molecularFormula\">Molecular Formula</Label>\n              <Input \n                id=\"molecularFormula\" \n                placeholder=\"e.g., N₂, O₂, Ar\"\n                value={details.molecularFormula} \n                onChange={(e) => handleChange('molecularFormula', e.target.value)} \n              />\n            </div>\n\n            <div>\n              <Label htmlFor=\"molecularWeight\">Molecular Weight</Label>\n              <Input \n                id=\"molecularWeight\" \n                placeholder=\"e.g., 28.014 g/mol\"\n                value={details.molecularWeight} \n                onChange={(e) => handleChange('molecularWeight', e.target.value)} \n              />\n            </div>\n\n            <div>\n              <Label htmlFor=\"appearance\">Appearance</Label>\n              <Input \n                id=\"appearance\" \n                placeholder=\"e.g., Colorless gas\"\n                value={details.appearance} \n                onChange={(e) => handleChange('appearance', e.target.value)} \n              />\n            </div>\n\n            <div>\n              <Label htmlFor=\"odor\">Odor</Label>\n              <Input \n                id=\"odor\" \n                placeholder=\"e.g., Odorless\"\n                value={details.odor} \n                onChange={(e) => handleChange('odor', e.target.value)} \n              />\n            </div>\n          </div>\n\n          <div>\n            <Label htmlFor=\"recommendedUse\">Recommended Use</Label>\n            <Input \n              id=\"recommendedUse\" \n              placeholder=\"e.g., Industrial gas, Laboratory use\"\n              value={details.recommendedUse} \n              onChange={(e) => handleChange('recommendedUse', e.target.value)} \n            />\n          </div>\n        </div>\n\n        {/* Supplier Information Section */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold border-b pb-2\">Supplier Information</h3>\n          \n          <div>\n            <Label htmlFor=\"supplierName\">Company Name</Label>\n            <Input \n              id=\"supplierName\" \n              placeholder=\"Your company name\"\n              value={details.supplierName} \n              onChange={(e) => handleChange('supplierName', e.target.value)} \n            />\n          </div>\n\n          <div>\n            <Label htmlFor=\"supplierAddress\">Address</Label>\n            <Input \n              id=\"supplierAddress\" \n              placeholder=\"Complete company address\"\n              value={details.supplierAddress} \n              onChange={(e) => handleChange('supplierAddress', e.target.value)} \n            />\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <Label htmlFor=\"supplierPhone\">Phone Number</Label>\n              <Input \n                id=\"supplierPhone\" \n                placeholder=\"e.g., +60 3-1234 5678\"\n                value={details.supplierPhone} \n                onChange={(e) => handleChange('supplierPhone', e.target.value)} \n              />\n            </div>\n\n            <div>\n              <Label htmlFor=\"emergencyPhone\">Emergency Phone</Label>\n              <Input \n                id=\"emergencyPhone\" \n                placeholder=\"24-hour emergency contact\"\n                value={details.emergencyPhone} \n                onChange={(e) => handleChange('emergencyPhone', e.target.value)} \n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Form Actions */}\n        <div className=\"flex gap-2 pt-4\">\n          <Button\n            variant=\"outline\"\n            onClick={handleClear}\n            className=\"flex-1\"\n          >\n            Clear Form\n          </Button>\n          <Button\n            variant=\"secondary\"\n            onClick={handleLoadSample}\n            className=\"flex-1\"\n          >\n            Load Sample Data\n          </Button>\n        </div>\n        <div className=\"text-center pt-2\">\n          {isFormValid ? (\n            <p className=\"text-sm text-green-600 font-medium py-2\">\n              ✓ Ready to generate SDS\n            </p>\n          ) : (\n            <p className=\"text-sm text-muted-foreground py-2\">\n              Fill required fields (*) to generate SDS\n            </p>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;AAGzB;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAcO,SAAS,QAAQ,EAAE,YAAY,EAAgB;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACrD,SAAS;QACT,WAAW;QACX,UAAU;QACV,aAAa;QACb,gBAAgB;QAChB,cAAc;QACd,iBAAiB;QACjB,eAAe;QACf,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,YAAY;QACZ,MAAM;IACR;IAEA,MAAM,eAAe,CAAC,OAA6B;QACjD,MAAM,aAAa;YAAE,GAAG,OAAO;YAAE,CAAC,MAAM,EAAE;QAAM;QAChD,WAAW;QACX,aAAa;IACf;IAEA,MAAM,cAAc;QAClB,MAAM,eAA+B;YACnC,SAAS;YACT,WAAW;YACX,UAAU;YACV,aAAa;YACb,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,eAAe;YACf,gBAAgB;YAChB,kBAAkB;YAClB,iBAAiB;YACjB,YAAY;YACZ,MAAM;QACR;QACA,WAAW;QACX,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,MAAM,gBAAgC;YACpC,SAAS;YACT,WAAW;YACX,UAAU;YACV,aAAa;YACb,gBAAgB;YAChB,cAAc;YACd,iBAAiB;YACjB,eAAe;YACf,gBAAgB;YAChB,kBAAkB;YAClB,iBAAiB;YACjB,YAAY;YACZ,MAAM;QACR;QACA,WAAW;QACX,aAAa;IACf;IAEA,MAAM,cAAc,QAAQ,OAAO,IAAI,QAAQ,WAAW,IAAI,QAAQ,SAAS,IAAI,QAAQ,QAAQ;IAEnG,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAiC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAA4C;;;;;;;;;;;;0BAI3D,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAEpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAU;;;;;;0DACzB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,QAAQ,OAAO;gDACtB,UAAU,CAAC,IAAM,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAI3D,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,8OAAC,kIAAA,CAAA,SAAM;gDAAC,eAAe,CAAC,QAAU,aAAa,eAAe;gDAAQ,OAAO,QAAQ,WAAW;;kEAC9F,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,IAAG;kEAChB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;kEACX,mHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,yBACnB,8OAAC,kIAAA,CAAA,aAAU;gEAAsB,OAAO,SAAS,KAAK;0EACnD,SAAS,KAAK;+DADA,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;kDAQvC,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;0DAC3B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,QAAQ,SAAS;gDACxB,UAAU,CAAC,IAAM,aAAa,aAAa,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAI7D,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,QAAQ,QAAQ;gDACvB,UAAU,CAAC,IAAM,aAAa,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAI5D,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAmB;;;;;;0DAClC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,QAAQ,gBAAgB;gDAC/B,UAAU,CAAC,IAAM,aAAa,oBAAoB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAIpE,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAkB;;;;;;0DACjC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,QAAQ,eAAe;gDAC9B,UAAU,CAAC,IAAM,aAAa,mBAAmB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAInE,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,QAAQ,UAAU;gDACzB,UAAU,CAAC,IAAM,aAAa,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAI9D,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAO;;;;;;0DACtB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,QAAQ,IAAI;gDACnB,UAAU,CAAC,IAAM,aAAa,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;0CAK1D,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAiB;;;;;;kDAChC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,QAAQ,cAAc;wCAC7B,UAAU,CAAC,IAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;kCAMpE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAEpD,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAe;;;;;;kDAC9B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,QAAQ,YAAY;wCAC3B,UAAU,CAAC,IAAM,aAAa,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0CAIhE,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAkB;;;;;;kDACjC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,QAAQ,eAAe;wCAC9B,UAAU,CAAC,IAAM,aAAa,mBAAmB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0CAInE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,QAAQ,aAAa;gDAC5B,UAAU,CAAC,IAAM,aAAa,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAIjE,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,QAAQ,cAAc;gDAC7B,UAAU,CAAC,IAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAOtE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAIH,8OAAC;wBAAI,WAAU;kCACZ,4BACC,8OAAC;4BAAE,WAAU;sCAA0C;;;;;iDAIvD,8OAAC;4BAAE,WAAU;sCAAqC;;;;;;;;;;;;;;;;;;;;;;;AAQ9D", "debugId": null}}, {"offset": {"line": 1161, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/experiment/SDS%20Augmented/sds-generator/src/components/SDSPDFTemplate.tsx"], "sourcesContent": ["// components/SDSPDFTemplate.tsx\n\"use client\";\n\nimport { GeneratedSDS, ProductDetails } from '@/types/sds';\nimport { forwardRef } from 'react';\n\ninterface SDSPDFTemplateProps {\n  content: GeneratedSDS;\n  productDetails: ProductDetails;\n}\n\nexport const SDSPDFTemplate = forwardRef<HTMLDivElement, SDSPDFTemplateProps>(\n  ({ content, productDetails }, ref) => {\n    const currentDate = new Date().toLocaleDateString('en-GB');\n    \n    return (\n      <div ref={ref} className=\"bg-white text-black font-sans text-sm leading-tight\">\n        {/* Page 1 */}\n        <div className=\"min-h-screen p-8 page-break-after\">\n          {/* Header */}\n          <div className=\"flex justify-between items-start mb-6\">\n            <div className=\"flex-1\">\n              <div className=\"text-xs text-gray-600 mb-1\">\n                Phone: +60 3-XXX XXX XX XX\n              </div>\n              <div className=\"text-xs text-gray-600\">\n                Emergency: +60 3-XXX XXX XX XX\n              </div>\n            </div>\n            <div className=\"bg-red-600 text-white px-4 py-2 font-bold text-lg\">\n              AGS\n            </div>\n          </div>\n\n          {/* Company Name */}\n          <div className=\"text-center mb-6\">\n            <h1 className=\"text-xl font-bold mb-2\">{productDetails.supplierName || 'ALPHA GAS SOLUTION SDN BHD'}</h1>\n            <div className=\"border-2 border-gray-800 p-4\">\n              <h2 className=\"text-lg font-bold mb-2\">SAFETY DATA SHEET</h2>\n              <h3 className=\"text-base font-semibold\">HYDROGEN COMPRESSED GAS</h3>\n            </div>\n          </div>\n\n          {/* Section 1: Product and Company Information */}\n          <div className=\"border border-gray-400 mb-4\">\n            <div className=\"bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400\">\n              SECTION 1 - PRODUCT AND COMPANY INFORMATION\n            </div>\n            <div className=\"p-3 space-y-2\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <div className=\"font-semibold\">Product Name:</div>\n                  <div>{productDetails.gasName}</div>\n                </div>\n                <div>\n                  <div className=\"font-semibold\">Product Code:</div>\n                  <div>{productDetails.gasName} ({productDetails.gasCategory} Gas)</div>\n                </div>\n              </div>\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <div className=\"font-semibold\">Other means of identification:</div>\n                  <div>\n                    {productDetails.gasName}<br/>\n                    {productDetails.gasName} Gas<br/>\n                    {productDetails.gasName} ({productDetails.gasCategory})\n                  </div>\n                </div>\n                <div>\n                  <div className=\"font-semibold\">Recommended use:</div>\n                  <div>{productDetails.recommendedUse || 'Industrial gas'}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Section 1.4: Supplier Details */}\n          <div className=\"border border-gray-400 mb-4\">\n            <div className=\"bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400\">\n              1.4 Details of supplier of the safety data sheet\n            </div>\n            <div className=\"p-3\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <div className=\"font-semibold\">Company Name:</div>\n                  <div>{productDetails.supplierName}</div>\n                </div>\n                <div>\n                  <div className=\"font-semibold\">Address:</div>\n                  <div>{productDetails.supplierAddress}</div>\n                </div>\n              </div>\n              <div className=\"grid grid-cols-2 gap-4 mt-2\">\n                <div>\n                  <div className=\"font-semibold\">Contact Number:</div>\n                  <div>{productDetails.supplierPhone}</div>\n                </div>\n                <div>\n                  <div className=\"font-semibold\">Emergency Contact Number:</div>\n                  <div>{productDetails.emergencyPhone}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Section 1.5: Emergency telephone number */}\n          <div className=\"border border-gray-400 mb-6\">\n            <div className=\"bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400\">\n              1.5 Emergency telephone number\n            </div>\n            <div className=\"p-3\">\n              <div className=\"font-semibold\">CHEMTREC:</div>\n              <div>******-527-3887 (24 hours)</div>\n            </div>\n          </div>\n\n          {/* Section 2: Hazards Identification */}\n          <div className=\"border border-gray-400 mb-4\">\n            <div className=\"bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400\">\n              SECTION 2 - HAZARDS IDENTIFICATION\n            </div>\n            <div className=\"p-3\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <div className=\"font-semibold\">2.1 Classification of the substance or mixture:</div>\n                  <div>Gases under pressure: {productDetails.gasCategory} gas</div>\n                </div>\n                <div>\n                  <div className=\"font-semibold\">2.2 Label elements:</div>\n                  <div>Signal Word: <span className=\"font-bold\">Danger</span></div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* GHS Pictograms */}\n          <div className=\"flex justify-center mb-6\">\n            <div className=\"flex space-x-4\">\n              {/* Flammable pictogram */}\n              <div className=\"w-16 h-16 border-2 border-red-600 bg-white flex items-center justify-center transform rotate-45\">\n                <div className=\"transform -rotate-45\">\n                  <div className=\"w-8 h-8 bg-red-600 clip-path-flame\"></div>\n                </div>\n              </div>\n              {/* Pressure pictogram */}\n              <div className=\"w-16 h-16 border-2 border-red-600 bg-white flex items-center justify-center transform rotate-45\">\n                <div className=\"transform -rotate-45 text-red-600 font-bold text-xs\">\n                  GAS\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Hazard Statements */}\n          <div className=\"border border-gray-400 mb-4\">\n            <div className=\"p-3\">\n              <div className=\"font-semibold mb-2\">Hazard Statement(s):</div>\n              <div className=\"text-sm\">\n                {productDetails.gasCategory === 'Refrigerated' ? (\n                  <>H281: Contains refrigerated gas; may cause cryogenic burns or injury</>\n                ) : (\n                  <>H280: Contains gas under pressure; may explode if heated</>\n                )}\n              </div>\n              <div className=\"font-semibold mb-2 mt-3\">Precautionary Statement(s):</div>\n              <div className=\"text-sm\">\n                {productDetails.gasCategory === 'Refrigerated' ? (\n                  <>\n                    P282: Wear cold insulating gloves/face shield/eye protection<br/>\n                    P336 + P315: Thaw frosted parts with lukewarm water. Get immediate medical advice<br/>\n                    P403: Store in a well-ventilated place\n                  </>\n                ) : (\n                  <>\n                    P410 + P403: Protect from sunlight. Store in a well-ventilated place\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Footer */}\n          <div className=\"absolute bottom-8 left-8 right-8 border-t-2 border-gray-800 pt-2\">\n            <div className=\"flex justify-between items-center text-xs\">\n              <div>\n                Phone: +60 3-XXX XXX XX XX<br/>\n                Emergency: +60 3-XXX XXX XX XX\n              </div>\n              <div className=\"bg-red-600 text-white px-2 py-1 font-bold\">\n                AGS\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page 2 */}\n        <div className=\"min-h-screen p-8 page-break-before\">\n          {/* Header */}\n          <div className=\"flex justify-between items-start mb-6\">\n            <div className=\"flex-1\">\n              <div className=\"text-xs text-gray-600 mb-1\">\n                Phone: +60 3-XXX XXX XX XX\n              </div>\n              <div className=\"text-xs text-gray-600\">\n                Emergency: +60 3-XXX XXX XX XX\n              </div>\n            </div>\n            <div className=\"bg-red-600 text-white px-4 py-2 font-bold text-lg\">\n              AGS\n            </div>\n          </div>\n\n          {/* Section 3: Composition/Information on Ingredients */}\n          <div className=\"border border-gray-400 mb-4\">\n            <div className=\"bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400\">\n              3.0 Composition/Information on Ingredients\n            </div>\n            <div className=\"p-3\">\n              <div className=\"font-semibold mb-2\">Pure Substance:</div>\n              <div className=\"text-sm space-y-1\">\n                <div>Chemical name: {productDetails.gasName}</div>\n                <div>CAS number: {productDetails.casNumber}</div>\n                <div>Concentration: &gt; 99.9%</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Section 4: First Aid Measures */}\n          <div className=\"border border-gray-400 mb-4\">\n            <div className=\"bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400\">\n              4.0 First Aid Measures (FIRST AID)\n            </div>\n            <div className=\"p-3\">\n              <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <div className=\"font-semibold\">EYE:</div>\n                  <div>Immediately flush eyes with plenty of water for at least 15 minutes. Get medical attention if irritation persists.</div>\n                </div>\n                <div>\n                  <div className=\"font-semibold\">SKIN:</div>\n                  <div>Remove contaminated clothing. Wash skin with soap and water. Get medical attention if irritation persists.</div>\n                </div>\n                <div>\n                  <div className=\"font-semibold\">INHALATION:</div>\n                  <div>Remove to fresh air immediately. If breathing is difficult, give oxygen. Get medical attention.</div>\n                </div>\n                <div>\n                  <div className=\"font-semibold\">INGESTION:</div>\n                  <div>Not applicable for gases.</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Section 5: Fire-fighting Measures */}\n          <div className=\"border border-gray-400 mb-4\">\n            <div className=\"bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400\">\n              5.0 Fire-fighting Measures\n            </div>\n            <div className=\"p-3 text-sm\">\n              <div className=\"font-semibold\">Suitable extinguishing media:</div>\n              <div className=\"mb-2\">Use extinguishing media appropriate for surrounding fire.</div>\n              <div className=\"font-semibold\">Special fire fighting procedures:</div>\n              <div className=\"mb-2\">Cool containers with water spray. Evacuate area if containers are exposed to fire.</div>\n              <div className=\"font-semibold\">Unusual fire and explosion hazards:</div>\n              <div>Containers may rupture or explode when heated.</div>\n            </div>\n          </div>\n\n          {/* Section 6: Accidental Release Measures */}\n          <div className=\"border border-gray-400 mb-4\">\n            <div className=\"bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400\">\n              6.0 Accidental Release Measures\n            </div>\n            <div className=\"p-3 text-sm\">\n              <div className=\"font-semibold\">Personal precautions:</div>\n              <div className=\"mb-2\">Evacuate area. Ensure adequate ventilation. Wear appropriate protective equipment.</div>\n              <div className=\"font-semibold\">Environmental precautions:</div>\n              <div className=\"mb-2\">Prevent gas from entering sewers, basements, or confined areas.</div>\n              <div className=\"font-semibold\">Methods for containment:</div>\n              <div>Stop leak if safe to do so. Allow gas to dissipate in well-ventilated area.</div>\n            </div>\n          </div>\n\n          {/* Section 7: Handling and Storage */}\n          <div className=\"border border-gray-400 mb-4\">\n            <div className=\"bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400\">\n              7.0 Handling and Storage\n            </div>\n            <div className=\"p-3 text-sm\">\n              <div className=\"font-semibold\">Handling:</div>\n              <div className=\"mb-2\">Use only in well-ventilated areas. Avoid breathing gas. Use appropriate personal protective equipment.</div>\n              <div className=\"font-semibold\">Storage:</div>\n              <div>Store in cool, dry, well-ventilated area. Protect from physical damage. Store upright and secure.</div>\n            </div>\n          </div>\n          \n          {/* Footer */}\n          <div className=\"absolute bottom-8 left-8 right-8 border-t-2 border-gray-800 pt-2\">\n            <div className=\"flex justify-between items-center text-xs\">\n              <div>\n                Phone: +60 3-XXX XXX XX XX<br/>\n                Emergency: +60 3-XXX XXX XX XX\n              </div>\n              <div className=\"bg-red-600 text-white px-2 py-1 font-bold\">\n                AGS\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n);\n\nSDSPDFTemplate.displayName = 'SDSPDFTemplate';\n"], "names": [], "mappings": "AAAA,gCAAgC;;;;;AAIhC;AAHA;;;AAUO,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrC,CAAC,EAAE,OAAO,EAAE,cAAc,EAAE,EAAE;IAC5B,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC;IAElD,qBACE,8OAAC;QAAI,KAAK;QAAK,WAAU;;0BAEvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAG5C,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAIzC,8OAAC;gCAAI,WAAU;0CAAoD;;;;;;;;;;;;kCAMrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B,eAAe,YAAY,IAAI;;;;;;0CACvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyB;;;;;;kDACvC,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;kCAK5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,8OAAC;kEAAK,eAAe,OAAO;;;;;;;;;;;;0DAE9B,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,8OAAC;;4DAAK,eAAe,OAAO;4DAAC;4DAAG,eAAe,WAAW;4DAAC;;;;;;;;;;;;;;;;;;;kDAG/D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,8OAAC;;4DACE,eAAe,OAAO;0EAAC,8OAAC;;;;;4DACxB,eAAe,OAAO;4DAAC;0EAAI,8OAAC;;;;;4DAC5B,eAAe,OAAO;4DAAC;4DAAG,eAAe,WAAW;4DAAC;;;;;;;;;;;;;0DAG1D,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,8OAAC;kEAAK,eAAe,cAAc,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,8OAAC;kEAAK,eAAe,YAAY;;;;;;;;;;;;0DAEnC,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,8OAAC;kEAAK,eAAe,eAAe;;;;;;;;;;;;;;;;;;kDAGxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,8OAAC;kEAAK,eAAe,aAAa;;;;;;;;;;;;0DAEpC,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,8OAAC;kEAAK,eAAe,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;kDAAI;;;;;;;;;;;;;;;;;;kCAKT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;;wDAAI;wDAAuB,eAAe,WAAW;wDAAC;;;;;;;;;;;;;sDAEzD,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;;wDAAI;sEAAa,8OAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;8CAInB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAsD;;;;;;;;;;;;;;;;;;;;;;kCAQ3E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAqB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;8CACZ,eAAe,WAAW,KAAK,+BAC9B;kDAAE;sEAEF;kDAAE;;;;;;;8CAGN,8OAAC;oCAAI,WAAU;8CAA0B;;;;;;8CACzC,8OAAC;oCAAI,WAAU;8CACZ,eAAe,WAAW,KAAK,+BAC9B;;4CAAE;0DAC4D,8OAAC;;;;;4CAAI;0DACgB,8OAAC;;;;;4CAAI;;qEAIxF;kDAAE;;;;;;;;;;;;;;;;;;kCASV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAI;sDACuB,8OAAC;;;;;wCAAI;;;;;;;8CAGjC,8OAAC;oCAAI,WAAU;8CAA4C;;;;;;;;;;;;;;;;;;;;;;;0BAQjE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAG5C,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAIzC,8OAAC;gCAAI,WAAU;0CAAoD;;;;;;;;;;;;kCAMrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAI;oDAAgB,eAAe,OAAO;;;;;;;0DAC3C,8OAAC;;oDAAI;oDAAa,eAAe,SAAS;;;;;;;0DAC1C,8OAAC;0DAAI;;;;;;;;;;;;;;;;;;;;;;;;kCAMX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;8DAAI;;;;;;;;;;;;sDAEP,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;8DAAI;;;;;;;;;;;;sDAEP,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;8DAAI;;;;;;;;;;;;sDAEP,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAI,WAAU;kDAAO;;;;;;kDACtB,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAI,WAAU;kDAAO;;;;;;kDACtB,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;kDAAI;;;;;;;;;;;;;;;;;;kCAKT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAI,WAAU;kDAAO;;;;;;kDACtB,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAI,WAAU;kDAAO;;;;;;kDACtB,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;kDAAI;;;;;;;;;;;;;;;;;;kCAKT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmE;;;;;;0CAGlF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAI,WAAU;kDAAO;;;;;;kDACtB,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;kDAAI;;;;;;;;;;;;;;;;;;kCAKT,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAI;sDACuB,8OAAC;;;;;wCAAI;;;;;;;8CAGjC,8OAAC;oCAAI,WAAU;8CAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvE;AAGF,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2369, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/experiment/SDS%20Augmented/sds-generator/src/lib/pdfGenerator.ts"], "sourcesContent": ["// lib/pdfGenerator.ts\nimport jsPDF from 'jspdf';\nimport html2canvas from 'html2canvas';\n\nexport interface PDFGenerationOptions {\n  filename?: string;\n  quality?: number;\n  format?: 'a4' | 'letter';\n  orientation?: 'portrait' | 'landscape';\n}\n\nexport async function generatePDFFromElement(\n  element: HTMLElement,\n  options: PDFGenerationOptions = {}\n): Promise<void> {\n  const {\n    filename = 'SDS_Document.pdf',\n    quality = 1.0,\n    format = 'a4',\n    orientation = 'portrait'\n  } = options;\n\n  try {\n    // Configure html2canvas options for better quality\n    const canvas = await html2canvas(element, {\n      scale: 2, // Higher scale for better quality\n      useCORS: true,\n      allowTaint: true,\n      backgroundColor: '#ffffff',\n      logging: false,\n      width: element.scrollWidth,\n      height: element.scrollHeight,\n    });\n\n    const imgData = canvas.toDataURL('image/png', quality);\n    \n    // Calculate PDF dimensions\n    const imgWidth = canvas.width;\n    const imgHeight = canvas.height;\n    \n    // A4 dimensions in mm\n    const pdfWidth = format === 'a4' ? 210 : 216; // A4 vs Letter\n    const pdfHeight = format === 'a4' ? 297 : 279;\n    \n    // Calculate scaling to fit content\n    const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);\n    const scaledWidth = imgWidth * ratio;\n    const scaledHeight = imgHeight * ratio;\n\n    // Create PDF\n    const pdf = new jsPDF({\n      orientation,\n      unit: 'mm',\n      format: format === 'a4' ? 'a4' : 'letter'\n    });\n\n    // Add image to PDF\n    pdf.addImage(\n      imgData,\n      'PNG',\n      0,\n      0,\n      scaledWidth,\n      scaledHeight,\n      undefined,\n      'FAST'\n    );\n\n    // If content is longer than one page, add additional pages\n    if (scaledHeight > pdfHeight) {\n      let remainingHeight = scaledHeight - pdfHeight;\n      let currentY = -pdfHeight;\n\n      while (remainingHeight > 0) {\n        pdf.addPage();\n        pdf.addImage(\n          imgData,\n          'PNG',\n          0,\n          currentY,\n          scaledWidth,\n          scaledHeight,\n          undefined,\n          'FAST'\n        );\n        currentY -= pdfHeight;\n        remainingHeight -= pdfHeight;\n      }\n    }\n\n    // Save the PDF\n    pdf.save(filename);\n  } catch (error) {\n    console.error('Error generating PDF:', error);\n    throw new Error('Failed to generate PDF. Please try again.');\n  }\n}\n\nexport async function generateSDSPDF(\n  element: HTMLElement,\n  gasName: string,\n  companyName?: string\n): Promise<void> {\n  const timestamp = new Date().toISOString().split('T')[0];\n  const sanitizedGasName = gasName.replace(/[^a-zA-Z0-9]/g, '_');\n  const sanitizedCompanyName = companyName ? companyName.replace(/[^a-zA-Z0-9]/g, '_') : 'Company';\n  \n  const filename = `SDS_${sanitizedGasName}_${sanitizedCompanyName}_${timestamp}.pdf`;\n  \n  await generatePDFFromElement(element, {\n    filename,\n    quality: 0.95,\n    format: 'a4',\n    orientation: 'portrait'\n  });\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;AACtB;AACA;;;AASO,eAAe,uBACpB,OAAoB,EACpB,UAAgC,CAAC,CAAC;IAElC,MAAM,EACJ,WAAW,kBAAkB,EAC7B,UAAU,GAAG,EACb,SAAS,IAAI,EACb,cAAc,UAAU,EACzB,GAAG;IAEJ,IAAI;QACF,mDAAmD;QACnD,MAAM,SAAS,MAAM,CAAA,GAAA,yJAAA,CAAA,UAAW,AAAD,EAAE,SAAS;YACxC,OAAO;YACP,SAAS;YACT,YAAY;YACZ,iBAAiB;YACjB,SAAS;YACT,OAAO,QAAQ,WAAW;YAC1B,QAAQ,QAAQ,YAAY;QAC9B;QAEA,MAAM,UAAU,OAAO,SAAS,CAAC,aAAa;QAE9C,2BAA2B;QAC3B,MAAM,WAAW,OAAO,KAAK;QAC7B,MAAM,YAAY,OAAO,MAAM;QAE/B,sBAAsB;QACtB,MAAM,WAAW,WAAW,OAAO,MAAM,KAAK,eAAe;QAC7D,MAAM,YAAY,WAAW,OAAO,MAAM;QAE1C,mCAAmC;QACnC,MAAM,QAAQ,KAAK,GAAG,CAAC,WAAW,UAAU,YAAY;QACxD,MAAM,cAAc,WAAW;QAC/B,MAAM,eAAe,YAAY;QAEjC,aAAa;QACb,MAAM,MAAM,IAAI,mJAAA,CAAA,UAAK,CAAC;YACpB;YACA,MAAM;YACN,QAAQ,WAAW,OAAO,OAAO;QACnC;QAEA,mBAAmB;QACnB,IAAI,QAAQ,CACV,SACA,OACA,GACA,GACA,aACA,cACA,WACA;QAGF,2DAA2D;QAC3D,IAAI,eAAe,WAAW;YAC5B,IAAI,kBAAkB,eAAe;YACrC,IAAI,WAAW,CAAC;YAEhB,MAAO,kBAAkB,EAAG;gBAC1B,IAAI,OAAO;gBACX,IAAI,QAAQ,CACV,SACA,OACA,GACA,UACA,aACA,cACA,WACA;gBAEF,YAAY;gBACZ,mBAAmB;YACrB;QACF;QAEA,eAAe;QACf,IAAI,IAAI,CAAC;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,eACpB,OAAoB,EACpB,OAAe,EACf,WAAoB;IAEpB,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACxD,MAAM,mBAAmB,QAAQ,OAAO,CAAC,iBAAiB;IAC1D,MAAM,uBAAuB,cAAc,YAAY,OAAO,CAAC,iBAAiB,OAAO;IAEvF,MAAM,WAAW,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,qBAAqB,CAAC,EAAE,UAAU,IAAI,CAAC;IAEnF,MAAM,uBAAuB,SAAS;QACpC;QACA,SAAS;QACT,QAAQ;QACR,aAAa;IACf;AACF", "debugId": null}}, {"offset": {"line": 2446, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/experiment/SDS%20Augmented/sds-generator/src/components/SDSPreview.tsx"], "sourcesContent": ["// components/SDSPreview.tsx\n\"use client\";\n\nimport { GeneratedSDS, SECTION_TITLES, ProductDetails } from '@/types/sds';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Download, FileText, FileDown } from 'lucide-react';\nimport { SDSPDFTemplate } from './SDSPDFTemplate';\nimport { generateSDSPDF } from '@/lib/pdfGenerator';\nimport { useRef, useState } from 'react';\n\ninterface SDSPreviewProps {\n  content: GeneratedSDS | null;\n  gasName?: string;\n  productDetails?: ProductDetails;\n}\n\nexport function SDSPreview({ content, gasName, productDetails }: SDSPreviewProps) {\n  const pdfTemplateRef = useRef<HTMLDivElement>(null);\n  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);\n\n  const handlePrint = () => {\n    window.print();\n  };\n\n  const handleExportTxt = () => {\n    if (!content || !gasName) return;\n\n    // Create a formatted text version for download\n    let exportContent = `SAFETY DATA SHEET\\n${gasName}\\n\\n`;\n\n    Object.entries(content).forEach(([key, value], index) => {\n      const sectionNumber = index + 1;\n      const sectionTitle = SECTION_TITLES[key as keyof typeof SECTION_TITLES];\n\n      exportContent += `SECTION ${sectionNumber}: ${sectionTitle.en.toUpperCase()}\\n`;\n      exportContent += `SEKSYEN ${sectionNumber}: ${sectionTitle.my.toUpperCase()}\\n\\n`;\n      exportContent += `English:\\n${value.en}\\n\\n`;\n      exportContent += `Bahasa Malaysia:\\n${value.my}\\n\\n`;\n      exportContent += '='.repeat(80) + '\\n\\n';\n    });\n\n    const blob = new Blob([exportContent], { type: 'text/plain' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `SDS_${gasName.replace(/\\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.txt`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  const handleExportPDF = async () => {\n    if (!content || !gasName || !productDetails || !pdfTemplateRef.current) return;\n\n    setIsGeneratingPDF(true);\n    try {\n      await generateSDSPDF(\n        pdfTemplateRef.current,\n        gasName,\n        productDetails.supplierName\n      );\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert('Failed to generate PDF. Please try again.');\n    } finally {\n      setIsGeneratingPDF(false);\n    }\n  };\n\n  if (!content) {\n    return (\n      <Card className=\"w-full h-fit\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <FileText className=\"h-5 w-5\" />\n            SDS Preview\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"text-center py-12 text-muted-foreground\">\n            <FileText className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n            <p className=\"text-lg font-medium mb-2\">No SDS Generated Yet</p>\n            <p className=\"text-sm\">\n              Fill out the form with required information to generate your Safety Data Sheet\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Header with actions */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <CardTitle className=\"flex items-center gap-2\">\n              <FileText className=\"h-5 w-5\" />\n              Safety Data Sheet Preview\n            </CardTitle>\n            <div className=\"flex gap-2\">\n              <Button variant=\"outline\" size=\"sm\" onClick={handlePrint}>\n                Print\n              </Button>\n              <Button variant=\"outline\" size=\"sm\" onClick={handleExportTxt}>\n                <Download className=\"h-4 w-4 mr-2\" />\n                Export TXT\n              </Button>\n              <Button\n                size=\"sm\"\n                onClick={handleExportPDF}\n                disabled={isGeneratingPDF}\n              >\n                <FileDown className=\"h-4 w-4 mr-2\" />\n                {isGeneratingPDF ? 'Generating...' : 'Export PDF'}\n              </Button>\n            </div>\n          </div>\n          {gasName && (\n            <p className=\"text-lg font-semibold text-primary\">{gasName}</p>\n          )}\n        </CardHeader>\n      </Card>\n\n      {/* SDS Sections */}\n      <div className=\"space-y-4 print:space-y-2\">\n        {Object.entries(content).map(([key, value], index) => {\n          const sectionNumber = index + 1;\n          const sectionTitle = SECTION_TITLES[key as keyof typeof SECTION_TITLES];\n          \n          return (\n            <Card key={key} className=\"print:break-inside-avoid\">\n              <CardHeader className=\"pb-3\">\n                <CardTitle className=\"text-lg\">\n                  Section {sectionNumber}: {sectionTitle.en}\n                </CardTitle>\n                <p className=\"text-sm text-muted-foreground\">\n                  Seksyen {sectionNumber}: {sectionTitle.my}\n                </p>\n              </CardHeader>\n              <CardContent className=\"pt-0\">\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                  <div>\n                    <h4 className=\"font-semibold mb-2 text-sm text-blue-700\">English</h4>\n                    <div className=\"text-sm leading-relaxed whitespace-pre-line bg-blue-50 p-3 rounded border\">\n                      {value.en}\n                    </div>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold mb-2 text-sm text-green-700\">Bahasa Malaysia</h4>\n                    <div className=\"text-sm leading-relaxed whitespace-pre-line bg-green-50 p-3 rounded border\">\n                      {value.my}\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          );\n        })}\n      </div>\n\n      {/* Footer */}\n      <Card className=\"print:break-inside-avoid\">\n        <CardContent className=\"pt-6\">\n          <div className=\"text-center text-sm text-muted-foreground\">\n            <p>This Safety Data Sheet was generated according to ICOP 2014 guidelines</p>\n            <p>Generated on: {new Date().toLocaleDateString()}</p>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Hidden PDF Template for generation */}\n      {content && productDetails && (\n        <div className=\"fixed -left-[9999px] -top-[9999px] opacity-0 pointer-events-none\">\n          <SDSPDFTemplate\n            ref={pdfTemplateRef}\n            content={content}\n            productDetails={productDetails}\n          />\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;AAG5B;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;AAgBO,SAAS,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAmB;IAC9E,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,cAAc;QAClB,OAAO,KAAK;IACd;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,WAAW,CAAC,SAAS;QAE1B,+CAA+C;QAC/C,IAAI,gBAAgB,CAAC,mBAAmB,EAAE,QAAQ,IAAI,CAAC;QAEvD,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;YAC7C,MAAM,gBAAgB,QAAQ;YAC9B,MAAM,eAAe,mHAAA,CAAA,iBAAc,CAAC,IAAmC;YAEvE,iBAAiB,CAAC,QAAQ,EAAE,cAAc,EAAE,EAAE,aAAa,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC;YAC/E,iBAAiB,CAAC,QAAQ,EAAE,cAAc,EAAE,EAAE,aAAa,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC;YACjF,iBAAiB,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,IAAI,CAAC;YAC5C,iBAAiB,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAC,IAAI,CAAC;YACpD,iBAAiB,IAAI,MAAM,CAAC,MAAM;QACpC;QAEA,MAAM,OAAO,IAAI,KAAK;YAAC;SAAc,EAAE;YAAE,MAAM;QAAa;QAC5D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,IAAI,EAAE,QAAQ,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QAChG,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,kBAAkB,CAAC,eAAe,OAAO,EAAE;QAExE,mBAAmB;QACnB,IAAI;YACF,MAAM,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EACjB,eAAe,OAAO,EACtB,SACA,eAAe,YAAY;QAE/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIpC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;IAOjC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,aAAU;;sCACT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,SAAS;sDAAa;;;;;;sDAG1D,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,SAAS;;8DAC3C,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS;4CACT,UAAU;;8DAEV,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDACnB,kBAAkB,kBAAkB;;;;;;;;;;;;;;;;;;;wBAI1C,yBACC,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;;;;;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;oBAC1C,MAAM,gBAAgB,QAAQ;oBAC9B,MAAM,eAAe,mHAAA,CAAA,iBAAc,CAAC,IAAmC;oBAEvE,qBACE,8OAAC,gIAAA,CAAA,OAAI;wBAAW,WAAU;;0CACxB,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;4CAAU;4CACpB;4CAAc;4CAAG,aAAa,EAAE;;;;;;;kDAE3C,8OAAC;wCAAE,WAAU;;4CAAgC;4CAClC;4CAAc;4CAAG,aAAa,EAAE;;;;;;;;;;;;;0CAG7C,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,8OAAC;oDAAI,WAAU;8DACZ,MAAM,EAAE;;;;;;;;;;;;sDAGb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA4C;;;;;;8DAC1D,8OAAC;oDAAI,WAAU;8DACZ,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;uBApBR;;;;;gBA2Bf;;;;;;0BAIF,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;;oCAAE;oCAAe,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;YAMpD,WAAW,gCACV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,oIAAA,CAAA,iBAAc;oBACb,KAAK;oBACL,SAAS;oBACT,gBAAgB;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 2882, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/experiment/SDS%20Augmented/sds-generator/src/lib/sdsGenerator.ts"], "sourcesContent": ["// lib/sdsGenerator.ts\nimport { ProductDetails, GeneratedSDS, SDSSection } from '@/types/sds';\n\n// This function acts like your \"AI Brain\" using fixed templates and logic\nexport function generateSDSContent(details: ProductDetails): GeneratedSDS {\n  \n  // --- Section 1: Identification ---\n  const getSection1Content = (): SDSSection => {\n    const en = `Product Identifier: ${details.gasName}\nCAS Number: ${details.casNumber}\nUN Number: ${details.unNumber}\nRecommended Use: ${details.recommendedUse || 'Industrial gas'}\n\nSupplier Details:\nCompany: ${details.supplierName}\nAddress: ${details.supplierAddress}\nTelephone: ${details.supplierPhone}\nEmergency Phone: ${details.emergencyPhone}`;\n\n    const my = `Pengecam Produk: ${details.gasName}\nNombor CAS: ${details.casNumber}\nNombor PBB: ${details.unNumber}\nKegunaan yang Disyorkan: ${details.recommendedUse || 'Gas industri'}\n\nButiran Pembekal:\nSyarikat: ${details.supplierName}\nAlamat: ${details.supplierAddress}\nTelefon: ${details.supplierPhone}\nTelefon Kecemasan: ${details.emergencyPhone}`;\n\n    return { en, my };\n  };\n\n  // --- Section 2: Hazard Identification (Critical Part) ---\n  const getSection2Content = (): SDSSection => {\n    let h_statement_en = \"H280: Contains gas under pressure; may explode if heated.\";\n    let h_statement_my = \"H280: Mengandungi gas di bawah tekanan; boleh meletup jika dipanaskan.\";\n    let p_statements_en = \"P410 + P403: Protect from sunlight. Store in a well-ventilated place.\";\n    let p_statements_my = \"P410 + P403: Lindungi daripada cahaya matahari. Simpan di tempat yang mempunyai pengudaraan yang baik.\";\n    let otherHazards_en = \"Other hazards: May displace oxygen and cause rapid suffocation.\";\n    let otherHazards_my = \"Bahaya-bahaya lain: Boleh menggantikan oksigen dan menyebabkan sesak nafas yang pantas.\";\n    let classification_en = `Gases under pressure - ${details.gasCategory} Gas`;\n    let classification_my = `Gas di bawah tekanan - Gas ${details.gasCategory}`;\n\n    if (details.gasCategory === 'Refrigerated') {\n      h_statement_en = \"H281: Contains refrigerated gas; may cause cryogenic burns or injury.\";\n      h_statement_my = \"H281: Mengandungi gas terdingin; boleh menyebabkan kelecuran atau kecederaan kriogenik.\";\n      p_statements_en = `P282: Wear cold insulating gloves/face shield/eye protection.\nP336 + P315: Thaw frosted parts with lukewarm water. Do not rub affected area. Get immediate medical advice/attention.\nP403: Store in a well-ventilated place.`;\n      p_statements_my = `P282: Pakai sarung tangan penebat sejuk/perisai muka/pelindung mata.\nP336 + P315: Cairkan bahagian yang terbeku dengan air suam. Jangan gosok bahagian yang terjejas. Dapatkan nasihat/rawatan perubatan dengan segera.\nP403: Simpan di tempat yang mempunyai pengudaraan yang baik.`;\n      otherHazards_en = \"Other hazards: Contact with liquid may cause cryogenic burns (frostbite). May displace oxygen and cause rapid suffocation.\";\n      otherHazards_my = \"Bahaya-bahaya lain: Sentuhan dengan cecair boleh menyebabkan kelecuran kriogenik (frostbite). Boleh menggantikan oksigen dan menyebabkan sesak nafas yang pantas.\";\n    }\n\n    const en = `Classification: ${classification_en}\nSignal Word: Warning\nHazard Statement(s): ${h_statement_en}\nPrecautionary Statement(s): ${p_statements_en}\n${otherHazards_en}`;\n\n    const my = `Pengelasan: ${classification_my}\nKata Isyarat: Awas\nPenyataan Bahaya: ${h_statement_my}\nPenyataan Berjaga-jaga: ${p_statements_my}\n${otherHazards_my}`;\n\n    return { en, my };\n  };\n\n  // --- Section 3: Composition/Information on Ingredients ---\n  const getSection3Content = (): SDSSection => {\n    const en = `Chemical Identity: ${details.gasName}\nCAS Number: ${details.casNumber}\nMolecular Formula: ${details.molecularFormula || 'Not specified'}\nMolecular Weight: ${details.molecularWeight || 'Not specified'}\nComposition: >99% (typical purity for industrial gases)\nImpurities: <1% (may include trace amounts of other gases)`;\n\n    const my = `Identiti Kimia: ${details.gasName}\nNombor CAS: ${details.casNumber}\nFormula Molekul: ${details.molecularFormula || 'Tidak dinyatakan'}\nBerat Molekul: ${details.molecularWeight || 'Tidak dinyatakan'}\nKomposisi: >99% (ketulenan biasa untuk gas industri)\nKekotoran: <1% (mungkin termasuk jumlah surih gas lain)`;\n\n    return { en, my };\n  };\n\n  // --- Section 4: First Aid Measures ---\n  const getSection4Content = (): SDSSection => {\n    let firstAid_en = `Inhalation: Remove person to fresh air. If breathing is difficult, give oxygen. Get medical attention.\nSkin Contact: In case of frostbite, warm affected area slowly with lukewarm water. Do not rub. Get medical attention.\nEye Contact: Flush with water for at least 15 minutes. Get medical attention.\nIngestion: Not applicable for gases.`;\n\n    let firstAid_my = `Penyedutan: Alihkan orang ke udara segar. Jika pernafasan sukar, berikan oksigen. Dapatkan rawatan perubatan.\nSentuhan Kulit: Sekiranya berlaku frostbite, hangatkan kawasan yang terjejas perlahan-lahan dengan air suam. Jangan gosok. Dapatkan rawatan perubatan.\nSentuhan Mata: Bilas dengan air selama sekurang-kurangnya 15 minit. Dapatkan rawatan perubatan.\nPenelanan: Tidak berkenaan untuk gas.`;\n\n    if (details.gasCategory === 'Refrigerated') {\n      firstAid_en = `Inhalation: Remove person to fresh air immediately. If breathing is difficult, give oxygen. Get medical attention.\nSkin Contact: In case of cryogenic burns/frostbite, warm affected area slowly with lukewarm water (not hot). Do not rub or massage. Remove contaminated clothing carefully. Get immediate medical attention.\nEye Contact: Flush immediately with lukewarm water for at least 15 minutes. Do not rub eyes. Get immediate medical attention.\nIngestion: Not applicable for gases.`;\n\n      firstAid_my = `Penyedutan: Alihkan orang ke udara segar dengan segera. Jika pernafasan sukar, berikan oksigen. Dapatkan rawatan perubatan.\nSentuhan Kulit: Sekiranya berlaku kelecuran kriogenik/frostbite, hangatkan kawasan yang terjejas perlahan-lahan dengan air suam (bukan panas). Jangan gosok atau urut. Tanggalkan pakaian yang tercemar dengan berhati-hati. Dapatkan rawatan perubatan segera.\nSentuhan Mata: Bilas segera dengan air suam selama sekurang-kurangnya 15 minit. Jangan gosok mata. Dapatkan rawatan perubatan segera.\nPenelanan: Tidak berkenaan untuk gas.`;\n    }\n\n    return { en: firstAid_en, my: firstAid_my };\n  };\n\n  // Generate all 16 sections\n  const sds: GeneratedSDS = {\n    section1: getSection1Content(),\n    section2: getSection2Content(),\n    section3: getSection3Content(),\n    section4: getSection4Content(),\n    section5: {\n      en: `Suitable Extinguishing Media: Use extinguishing media appropriate for surrounding fire.\nSpecial Fire Fighting Procedures: Cool containers with water spray. Evacuate area if containers are exposed to fire.\nUnusual Fire and Explosion Hazards: Containers may rupture or explode when heated.`,\n      my: `Media Pemadam yang Sesuai: Gunakan media pemadam yang sesuai untuk kebakaran sekeliling.\nProsedur Memadam Api Khas: Sejukkan bekas dengan semburan air. Kosongkan kawasan jika bekas terdedah kepada api.\nBahaya Kebakaran dan Letupan Luar Biasa: Bekas mungkin pecah atau meletup apabila dipanaskan.`\n    },\n    section6: {\n      en: `Personal Precautions: Evacuate area. Ensure adequate ventilation. Wear appropriate protective equipment.\nEnvironmental Precautions: Prevent gas from entering sewers, basements, or confined areas.\nMethods for Containment: Stop leak if safe to do so. Allow gas to dissipate in well-ventilated area.`,\n      my: `Langkah Berjaga-jaga Peribadi: Kosongkan kawasan. Pastikan pengudaraan yang mencukupi. Pakai peralatan pelindung yang sesuai.\nLangkah Berjaga-jaga Alam Sekitar: Elakkan gas daripada memasuki pembetung, ruang bawah tanah, atau kawasan terkurung.\nKaedah untuk Pembendungan: Hentikan kebocoran jika selamat untuk berbuat demikian. Biarkan gas tersebar di kawasan yang mempunyai pengudaraan yang baik.`\n    },\n    section7: {\n      en: `Handling: Use only in well-ventilated areas. Avoid breathing gas. Use appropriate personal protective equipment.\nStorage: Store in cool, dry, well-ventilated area. Protect from physical damage. Store upright and secure.`,\n      my: `Pengendalian: Gunakan hanya di kawasan yang mempunyai pengudaraan yang baik. Elakkan menghidu gas. Gunakan peralatan pelindung diri yang sesuai.\nPenyimpanan: Simpan di kawasan yang sejuk, kering, dan mempunyai pengudaraan yang baik. Lindungi daripada kerosakan fizikal. Simpan tegak dan selamat.`\n    },\n    section8: {\n      en: `Exposure Limits: Follow local occupational exposure limits.\nEngineering Controls: Use adequate ventilation to control airborne concentrations.\nPersonal Protective Equipment: Safety glasses, protective gloves, appropriate respiratory protection if needed.`,\n      my: `Had Pendedahan: Ikut had pendedahan pekerjaan tempatan.\nKawalan Kejuruteraan: Gunakan pengudaraan yang mencukupi untuk mengawal kepekatan di udara.\nPeralatan Pelindung Diri: Cermin mata keselamatan, sarung tangan pelindung, perlindungan pernafasan yang sesuai jika diperlukan.`\n    },\n    section9: {\n      en: `Appearance: ${details.appearance || 'Colorless gas'}\nOdor: ${details.odor || 'Odorless'}\nPhysical State: Gas under pressure\nBoiling Point: Varies by gas type\nDensity: Varies by gas type`,\n      my: `Penampilan: ${details.appearance || 'Gas tidak berwarna'}\nBau: ${details.odor || 'Tidak berbau'}\nKeadaan Fizikal: Gas di bawah tekanan\nTakat Didih: Berbeza mengikut jenis gas\nKetumpatan: Berbeza mengikut jenis gas`\n    },\n    section10: {\n      en: `Stability: Stable under normal conditions.\nReactivity: Generally non-reactive under normal conditions.\nIncompatible Materials: Varies by specific gas - consult gas-specific documentation.`,\n      my: `Kestabilan: Stabil di bawah keadaan normal.\nKereaktifan: Umumnya tidak reaktif di bawah keadaan normal.\nBahan Tidak Serasi: Berbeza mengikut gas tertentu - rujuk dokumentasi khusus gas.`\n    },\n    section11: {\n      en: `Acute Effects: May cause asphyxiation by displacement of oxygen. High concentrations may cause dizziness and drowsiness.\nChronic Effects: No known chronic effects from normal industrial use.`,\n      my: `Kesan Akut: Boleh menyebabkan sesak nafas dengan menggantikan oksigen. Kepekatan tinggi boleh menyebabkan pening dan mengantuk.\nKesan Kronik: Tiada kesan kronik yang diketahui daripada penggunaan industri biasa.`\n    },\n    section12: {\n      en: `Ecotoxicity: No specific ecological data available. Not expected to be harmful to aquatic life.\nPersistence: Gas will dissipate rapidly in atmosphere.`,\n      my: `Ekotoksisiti: Tiada data ekologi khusus tersedia. Tidak dijangka membahayakan hidupan akuatik.\nKegigihan: Gas akan tersebar dengan cepat di atmosfera.`\n    },\n    section13: {\n      en: `Disposal: Return unused product in original container to supplier. Do not puncture or incinerate containers.\nContaminated Packaging: Handle as for unused product.`,\n      my: `Pelupusan: Kembalikan produk yang tidak digunakan dalam bekas asal kepada pembekal. Jangan menebuk atau membakar bekas.\nPembungkusan Tercemar: Kendalikan seperti produk yang tidak digunakan.`\n    },\n    section14: {\n      en: `UN Number: ${details.unNumber}\nUN Proper Shipping Name: ${details.gasName}\nTransport Hazard Class: 2.2 (Non-flammable, non-toxic gas)\nPacking Group: Not applicable`,\n      my: `Nombor PBB: ${details.unNumber}\nNama Penghantaran PBB yang Sesuai: ${details.gasName}\nKelas Bahaya Pengangkutan: 2.2 (Gas tidak mudah terbakar, tidak toksik)\nKumpulan Pembungkusan: Tidak berkenaan`\n    },\n    section15: {\n      en: `Malaysian Regulations: Comply with OSHA 1994, FMA 1967, and local regulations.\nInternational Regulations: Follow UN recommendations for transport of dangerous goods.`,\n      my: `Peraturan Malaysia: Patuhi OSHA 1994, FMA 1967, dan peraturan tempatan.\nPeraturan Antarabangsa: Ikut cadangan PBB untuk pengangkutan barang berbahaya.`\n    },\n    section16: {\n      en: `Date of Preparation: ${new Date().toLocaleDateString()}\nVersion: 1.0\nPrepared by: ${details.supplierName}\nThis SDS was generated according to ICOP 2014 guidelines.`,\n      my: `Tarikh Penyediaan: ${new Date().toLocaleDateString()}\nVersi: 1.0\nDisediakan oleh: ${details.supplierName}\nSDS ini dijana mengikut garis panduan ICOP 2014.`\n    }\n  };\n\n  return sds;\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AAIf,SAAS,mBAAmB,OAAuB;IAExD,oCAAoC;IACpC,MAAM,qBAAqB;QACzB,MAAM,KAAK,CAAC,oBAAoB,EAAE,QAAQ,OAAO,CAAC;YAC1C,EAAE,QAAQ,SAAS,CAAC;WACrB,EAAE,QAAQ,QAAQ,CAAC;iBACb,EAAE,QAAQ,cAAc,IAAI,iBAAiB;;;SAGrD,EAAE,QAAQ,YAAY,CAAC;SACvB,EAAE,QAAQ,eAAe,CAAC;WACxB,EAAE,QAAQ,aAAa,CAAC;iBAClB,EAAE,QAAQ,cAAc,EAAE;QAEvC,MAAM,KAAK,CAAC,iBAAiB,EAAE,QAAQ,OAAO,CAAC;YACvC,EAAE,QAAQ,SAAS,CAAC;YACpB,EAAE,QAAQ,QAAQ,CAAC;yBACN,EAAE,QAAQ,cAAc,IAAI,eAAe;;;UAG1D,EAAE,QAAQ,YAAY,CAAC;QACzB,EAAE,QAAQ,eAAe,CAAC;SACzB,EAAE,QAAQ,aAAa,CAAC;mBACd,EAAE,QAAQ,cAAc,EAAE;QAEzC,OAAO;YAAE;YAAI;QAAG;IAClB;IAEA,2DAA2D;IAC3D,MAAM,qBAAqB;QACzB,IAAI,iBAAiB;QACrB,IAAI,iBAAiB;QACrB,IAAI,kBAAkB;QACtB,IAAI,kBAAkB;QACtB,IAAI,kBAAkB;QACtB,IAAI,kBAAkB;QACtB,IAAI,oBAAoB,CAAC,uBAAuB,EAAE,QAAQ,WAAW,CAAC,IAAI,CAAC;QAC3E,IAAI,oBAAoB,CAAC,2BAA2B,EAAE,QAAQ,WAAW,EAAE;QAE3E,IAAI,QAAQ,WAAW,KAAK,gBAAgB;YAC1C,iBAAiB;YACjB,iBAAiB;YACjB,kBAAkB,CAAC;;uCAEc,CAAC;YAClC,kBAAkB,CAAC;;4DAEmC,CAAC;YACvD,kBAAkB;YAClB,kBAAkB;QACpB;QAEA,MAAM,KAAK,CAAC,gBAAgB,EAAE,kBAAkB;;qBAE/B,EAAE,eAAe;4BACV,EAAE,gBAAgB;AAC9C,EAAE,iBAAiB;QAEf,MAAM,KAAK,CAAC,YAAY,EAAE,kBAAkB;;kBAE9B,EAAE,eAAe;wBACX,EAAE,gBAAgB;AAC1C,EAAE,iBAAiB;QAEf,OAAO;YAAE;YAAI;QAAG;IAClB;IAEA,4DAA4D;IAC5D,MAAM,qBAAqB;QACzB,MAAM,KAAK,CAAC,mBAAmB,EAAE,QAAQ,OAAO,CAAC;YACzC,EAAE,QAAQ,SAAS,CAAC;mBACb,EAAE,QAAQ,gBAAgB,IAAI,gBAAgB;kBAC/C,EAAE,QAAQ,eAAe,IAAI,gBAAgB;;0DAEL,CAAC;QAEvD,MAAM,KAAK,CAAC,gBAAgB,EAAE,QAAQ,OAAO,CAAC;YACtC,EAAE,QAAQ,SAAS,CAAC;iBACf,EAAE,QAAQ,gBAAgB,IAAI,mBAAmB;eACnD,EAAE,QAAQ,eAAe,IAAI,mBAAmB;;uDAER,CAAC;QAEpD,OAAO;YAAE;YAAI;QAAG;IAClB;IAEA,wCAAwC;IACxC,MAAM,qBAAqB;QACzB,IAAI,cAAc,CAAC;;;oCAGa,CAAC;QAEjC,IAAI,cAAc,CAAC;;;qCAGc,CAAC;QAElC,IAAI,QAAQ,WAAW,KAAK,gBAAgB;YAC1C,cAAc,CAAC;;;oCAGe,CAAC;YAE/B,cAAc,CAAC;;;qCAGgB,CAAC;QAClC;QAEA,OAAO;YAAE,IAAI;YAAa,IAAI;QAAY;IAC5C;IAEA,2BAA2B;IAC3B,MAAM,MAAoB;QACxB,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;YACR,IAAI,CAAC;;kFAEuE,CAAC;YAC7E,IAAI,CAAC;;6FAEkF,CAAC;QAC1F;QACA,UAAU;YACR,IAAI,CAAC;;oGAEyF,CAAC;YAC/F,IAAI,CAAC;;wJAE6I,CAAC;QACrJ;QACA,UAAU;YACR,IAAI,CAAC;0GAC+F,CAAC;YACrG,IAAI,CAAC;sJAC2I,CAAC;QACnJ;QACA,UAAU;YACR,IAAI,CAAC;;+GAEoG,CAAC;YAC1G,IAAI,CAAC;;gIAEqH,CAAC;QAC7H;QACA,UAAU;YACR,IAAI,CAAC,YAAY,EAAE,QAAQ,UAAU,IAAI,gBAAgB;MACzD,EAAE,QAAQ,IAAI,IAAI,WAAW;;;2BAGR,CAAC;YACtB,IAAI,CAAC,YAAY,EAAE,QAAQ,UAAU,IAAI,qBAAqB;KAC/D,EAAE,QAAQ,IAAI,IAAI,eAAe;;;sCAGA,CAAC;QACnC;QACA,WAAW;YACT,IAAI,CAAC;;oFAEyE,CAAC;YAC/E,IAAI,CAAC;;iFAEsE,CAAC;QAC9E;QACA,WAAW;YACT,IAAI,CAAC;qEAC0D,CAAC;YAChE,IAAI,CAAC;mFACwE,CAAC;QAChF;QACA,WAAW;YACT,IAAI,CAAC;sDAC2C,CAAC;YACjD,IAAI,CAAC;uDAC4C,CAAC;QACpD;QACA,WAAW;YACT,IAAI,CAAC;qDAC0C,CAAC;YAChD,IAAI,CAAC;sEAC2D,CAAC;QACnE;QACA,WAAW;YACT,IAAI,CAAC,WAAW,EAAE,QAAQ,QAAQ,CAAC;yBAChB,EAAE,QAAQ,OAAO,CAAC;;6BAEd,CAAC;YACxB,IAAI,CAAC,YAAY,EAAE,QAAQ,QAAQ,CAAC;mCACP,EAAE,QAAQ,OAAO,CAAC;;sCAEf,CAAC;QACnC;QACA,WAAW;YACT,IAAI,CAAC;sFAC2E,CAAC;YACjF,IAAI,CAAC;8EACmE,CAAC;QAC3E;QACA,WAAW;YACT,IAAI,CAAC,qBAAqB,EAAE,IAAI,OAAO,kBAAkB,GAAG;;aAErD,EAAE,QAAQ,YAAY,CAAC;yDACqB,CAAC;YACpD,IAAI,CAAC,mBAAmB,EAAE,IAAI,OAAO,kBAAkB,GAAG;;iBAE/C,EAAE,QAAQ,YAAY,CAAC;gDACQ,CAAC;QAC7C;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/experiment/SDS%20Augmented/sds-generator/src/app/page.tsx"], "sourcesContent": ["// app/page.tsx\n\"use client\";\n\nimport { useState } from 'react';\nimport { SDSForm } from '@/components/SDSForm';\nimport { SDSPreview } from '@/components/SDSPreview';\nimport { ProductDetails, GeneratedSDS } from '@/types/sds';\nimport { generateSDSContent } from '@/lib/sdsGenerator';\n\nexport default function HomePage() {\n  const [sdsData, setSdsData] = useState<GeneratedSDS | null>(null);\n  const [currentGasName, setCurrentGasName] = useState<string>('');\n  const [currentProductDetails, setCurrentProductDetails] = useState<ProductDetails | null>(null);\n\n  const handleDataChange = (data: ProductDetails) => {\n    // Only generate if we have the minimum required data\n    if (data.gasName && data.gasCategory && data.casNumber && data.unNumber) {\n      const generatedContent = generateSDSContent(data);\n      setSdsData(generatedContent);\n      setCurrentGasName(data.gasName);\n      setCurrentProductDetails(data);\n    } else {\n      setSdsData(null);\n      setCurrentGasName('');\n      setCurrentProductDetails(null);\n    }\n  };\n\n  return (\n    <main className=\"min-h-screen bg-gradient-to-br from-blue-50 to-green-50\">\n      <div className=\"container mx-auto p-4 lg:p-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">\n            SDS Generator\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Generate professional Safety Data Sheets for compressed gases following ICOP 2014 guidelines.\n            Simply fill in the product details and get a complete bilingual SDS instantly.\n          </p>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-8 max-w-7xl mx-auto\">\n          {/* Form Section */}\n          <div className=\"order-2 xl:order-1\">\n            <SDSForm onDataChange={handleDataChange} />\n          </div>\n\n          {/* Preview Section */}\n          <div className=\"order-1 xl:order-2\">\n            <SDSPreview\n              content={sdsData}\n              gasName={currentGasName}\n              productDetails={currentProductDetails}\n            />\n          </div>\n        </div>\n\n        {/* Footer */}\n        <footer className=\"mt-16 text-center text-sm text-gray-500\">\n          <p>\n            Built with Next.js, TypeScript, and shadcn/ui • Following ICOP 2014 Guidelines\n          </p>\n          <p className=\"mt-1\">\n            © {new Date().getFullYear()} SDS Generator Tool\n          </p>\n        </footer>\n      </div>\n    </main>\n  );\n}\n"], "names": [], "mappings": "AAAA,eAAe;;;;;AAGf;AACA;AACA;AAEA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAE1F,MAAM,mBAAmB,CAAC;QACxB,qDAAqD;QACrD,IAAI,KAAK,OAAO,IAAI,KAAK,WAAW,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,EAAE;YACvE,MAAM,mBAAmB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAC5C,WAAW;YACX,kBAAkB,KAAK,OAAO;YAC9B,yBAAyB;QAC3B,OAAO;YACL,WAAW;YACX,kBAAkB;YAClB,yBAAyB;QAC3B;IACF;IAEA,qBACE,8OAAC;QAAK,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAO;gCAAC,cAAc;;;;;;;;;;;sCAIzB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,aAAU;gCACT,SAAS;gCACT,SAAS;gCACT,gBAAgB;;;;;;;;;;;;;;;;;8BAMtB,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;sCAAE;;;;;;sCAGH,8OAAC;4BAAE,WAAU;;gCAAO;gCACf,IAAI,OAAO,WAAW;gCAAG;;;;;;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}]}