# SDS Generator - Professional Safety Data Sheet Generator

A modern web application for generating professional Safety Data Sheets (SDS) for compressed gases following ICOP 2014 guidelines. Built with Next.js, TypeScript, and shadcn/ui.

## Features

### 🎯 **Smart Form Architecture**
- Real-time SDS generation as you type
- Intelligent validation and user guidance
- Professional UI with shadcn/ui components
- Sample data loading for quick testing

### 📋 **ICOP 2014 Compliance**
- Complete 16-section SDS generation
- Bilingual output (English & Bahasa Malaysia)
- Gas category-specific templates:
  - Compressed Gas
  - Liquefied Gas
  - Refrigerated Liquefied Gas
  - Dissolved Gas

### 📄 **Professional PDF Export**
- High-quality PDF generation matching industry standards
- Professional layout similar to AGS format
- Proper sectioning and formatting
- Company branding integration

### 🌐 **Modern Technology Stack**
- **Next.js 15** with App Router
- **TypeScript** for type safety
- **shadcn/ui** for professional components
- **Tailwind CSS** for styling
- **jsPDF & html2canvas** for PDF generation

## Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd sds-generator
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Usage

### 1. **Fill the Form**
- Enter gas details (Gas Name, Category, CAS Number, UN Number are required)
- Add supplier information
- Use "Load Sample Data" for quick testing

### 2. **Real-time Preview**
- Watch the SDS generate automatically as you type
- View both English and Bahasa Malaysia versions
- See all 16 sections formatted professionally

### 3. **Export Options**
- **Print**: Use browser's print function
- **Export TXT**: Download as formatted text file
- **Export PDF**: Generate professional PDF matching industry standards

### 4. **PDF Features**
- Professional layout with company branding
- Proper sectioning and formatting
- High-quality output suitable for official use
- Automatic filename generation with timestamp

## Project Structure

```
src/
├── app/                    # Next.js app router
├── components/
│   ├── ui/                # shadcn/ui components
│   ├── SDSForm.tsx        # Main input form
│   ├── SDSPreview.tsx     # Real-time preview
│   └── SDSPDFTemplate.tsx # Professional PDF template
├── lib/
│   ├── sdsGenerator.ts    # Core SDS generation logic
│   └── pdfGenerator.ts    # PDF generation utilities
└── types/
    └── sds.ts            # TypeScript definitions
```

## Key Components

### SDS Generation Logic
The core logic in `lib/sdsGenerator.ts` implements ICOP 2014 rules as deterministic code, ensuring:
- Consistent, compliant output
- No AI hallucination risks
- Gas category-specific templates
- Proper hazard classifications

### PDF Template
The `SDSPDFTemplate.tsx` component provides:
- Professional layout matching industry standards
- Company branding integration
- Proper sectioning and formatting
- Print-optimized styling

## Customization

### Adding New Gas Categories
1. Update the `GAS_CATEGORIES` in `types/sds.ts`
2. Add logic in `sdsGenerator.ts` for the new category
3. Update PDF template if needed

### Branding Customization
- Modify the PDF template header/footer
- Update company colors and logos
- Customize the layout as needed

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues or questions, please create an issue in the repository.
