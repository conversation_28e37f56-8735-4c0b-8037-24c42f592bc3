// components/SDSPreview.tsx
"use client";

import { GeneratedSDS, SECTION_TITLES, ProductDetails } from '@/types/sds';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, FileText, FileDown } from 'lucide-react';
import { SDSPDFTemplate } from './SDSPDFTemplate';
import { generateSDSPDF, generateSDSPDFDirect } from '@/lib/pdfGenerator';
import { useRef, useState } from 'react';

interface SDSPreviewProps {
  content: GeneratedSDS | null;
  gasName?: string;
  productDetails?: ProductDetails;
}

export function SDSPreview({ content, gasName, productDetails }: SDSPreviewProps) {
  const pdfTemplateRef = useRef<HTMLDivElement>(null);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  const handlePrint = () => {
    window.print();
  };

  const handleExportTxt = () => {
    if (!content || !gasName) return;

    // Create a formatted text version for download
    let exportContent = `SAFETY DATA SHEET\n${gasName}\n\n`;

    Object.entries(content).forEach(([key, value], index) => {
      const sectionNumber = index + 1;
      const sectionTitle = SECTION_TITLES[key as keyof typeof SECTION_TITLES];

      exportContent += `SECTION ${sectionNumber}: ${sectionTitle.en.toUpperCase()}\n`;
      exportContent += `SEKSYEN ${sectionNumber}: ${sectionTitle.my.toUpperCase()}\n\n`;
      exportContent += `English:\n${value.en}\n\n`;
      exportContent += `Bahasa Malaysia:\n${value.my}\n\n`;
      exportContent += '='.repeat(80) + '\n\n';
    });

    const blob = new Blob([exportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `SDS_${gasName.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleExportPDF = async () => {
    if (!content || !gasName || !productDetails) return;

    setIsGeneratingPDF(true);
    try {
      // Try HTML-to-PDF first, fallback to direct generation
      if (pdfTemplateRef.current) {
        try {
          await generateSDSPDF(
            pdfTemplateRef.current,
            gasName,
            productDetails.supplierName
          );
        } catch (htmlError) {
          console.log('HTML-to-PDF failed, using direct generation...');
          await generateSDSPDFDirect(
            content,
            productDetails,
            gasName,
            productDetails.supplierName
          );
        }
      } else {
        // Use direct generation if template ref is not available
        await generateSDSPDFDirect(
          content,
          productDetails,
          gasName,
          productDetails.supplierName
        );
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  if (!content) {
    return (
      <Card className="w-full h-fit">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            SDS Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-muted-foreground">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">No SDS Generated Yet</p>
            <p className="text-sm">
              Fill out the form with required information to generate your Safety Data Sheet
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Safety Data Sheet Preview
            </CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handlePrint}>
                Print
              </Button>
              <Button variant="outline" size="sm" onClick={handleExportTxt}>
                <Download className="h-4 w-4 mr-2" />
                Export TXT
              </Button>
              <Button
                size="sm"
                onClick={handleExportPDF}
                disabled={isGeneratingPDF}
              >
                <FileDown className="h-4 w-4 mr-2" />
                {isGeneratingPDF ? 'Generating...' : 'Export PDF'}
              </Button>
            </div>
          </div>
          {gasName && (
            <p className="text-lg font-semibold text-primary">{gasName}</p>
          )}
        </CardHeader>
      </Card>

      {/* SDS Sections */}
      <div className="space-y-4 print:space-y-2">
        {Object.entries(content).map(([key, value], index) => {
          const sectionNumber = index + 1;
          const sectionTitle = SECTION_TITLES[key as keyof typeof SECTION_TITLES];
          
          return (
            <Card key={key} className="print:break-inside-avoid">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">
                  Section {sectionNumber}: {sectionTitle.en}
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Seksyen {sectionNumber}: {sectionTitle.my}
                </p>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2 text-sm text-blue-700">English</h4>
                    <div className="text-sm leading-relaxed whitespace-pre-line bg-blue-50 p-3 rounded border">
                      {value.en}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-sm text-green-700">Bahasa Malaysia</h4>
                    <div className="text-sm leading-relaxed whitespace-pre-line bg-green-50 p-3 rounded border">
                      {value.my}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Footer */}
      <Card className="print:break-inside-avoid">
        <CardContent className="pt-6">
          <div className="text-center text-sm text-muted-foreground">
            <p>This Safety Data Sheet was generated according to ICOP 2014 guidelines</p>
            <p>Generated on: {new Date().toLocaleDateString()}</p>
          </div>
        </CardContent>
      </Card>

      {/* Hidden PDF Template for generation */}
      {content && productDetails && (
        <div
          style={{
            position: 'absolute',
            left: '-9999px',
            top: '-9999px',
            opacity: 0,
            pointerEvents: 'none',
            width: '210mm',
            backgroundColor: 'white'
          }}
        >
          <SDSPDFTemplate
            ref={pdfTemplateRef}
            content={content}
            productDetails={productDetails}
          />
        </div>
      )}
    </div>
  );
}
