// components/SDSForm.tsx
"use client";

import { useState } from 'react';
import { ProductDetails, GAS_CATEGORIES } from '@/types/sds';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

interface SDSFormProps {
  onDataChange: (data: ProductDetails) => void;
}

export function SDSForm({ onDataChange }: SDSFormProps) {
  const [details, setDetails] = useState<ProductDetails>({
    gasName: '',
    casNumber: '',
    unNumber: '',
    gasCategory: '',
    recommendedUse: '',
    supplierName: '',
    supplierAddress: '',
    supplierPhone: '',
    emergencyPhone: '',
    molecularFormula: '',
    molecularWeight: '',
    appearance: '',
    odor: '',
  });

  const handleChange = (field: keyof ProductDetails, value: string) => {
    const newDetails = { ...details, [field]: value };
    setDetails(newDetails);
    onDataChange(newDetails);
  };

  const handleClear = () => {
    const emptyDetails: ProductDetails = {
      gasName: '',
      casNumber: '',
      unNumber: '',
      gasCategory: '',
      recommendedUse: '',
      supplierName: '',
      supplierAddress: '',
      supplierPhone: '',
      emergencyPhone: '',
      molecularFormula: '',
      molecularWeight: '',
      appearance: '',
      odor: '',
    };
    setDetails(emptyDetails);
    onDataChange(emptyDetails);
  };

  const isFormValid = details.gasName && details.gasCategory && details.casNumber && details.unNumber;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">SDS Generator</CardTitle>
        <p className="text-sm text-muted-foreground text-center">
          Generate Safety Data Sheets following ICOP 2014 guidelines
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Product Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold border-b pb-2">Product Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="gasName">Gas Name *</Label>
              <Input 
                id="gasName" 
                placeholder="e.g., Nitrogen, Oxygen, Argon"
                value={details.gasName} 
                onChange={(e) => handleChange('gasName', e.target.value)} 
              />
            </div>

            <div>
              <Label htmlFor="gasCategory">Gas Category *</Label>
              <Select onValueChange={(value) => handleChange('gasCategory', value)} value={details.gasCategory}>
                <SelectTrigger id="gasCategory">
                  <SelectValue placeholder="Select gas category..." />
                </SelectTrigger>
                <SelectContent>
                  {GAS_CATEGORIES.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="casNumber">CAS Number *</Label>
              <Input 
                id="casNumber" 
                placeholder="e.g., 7727-37-9"
                value={details.casNumber} 
                onChange={(e) => handleChange('casNumber', e.target.value)} 
              />
            </div>

            <div>
              <Label htmlFor="unNumber">UN Number *</Label>
              <Input 
                id="unNumber" 
                placeholder="e.g., UN1066"
                value={details.unNumber} 
                onChange={(e) => handleChange('unNumber', e.target.value)} 
              />
            </div>

            <div>
              <Label htmlFor="molecularFormula">Molecular Formula</Label>
              <Input 
                id="molecularFormula" 
                placeholder="e.g., N₂, O₂, Ar"
                value={details.molecularFormula} 
                onChange={(e) => handleChange('molecularFormula', e.target.value)} 
              />
            </div>

            <div>
              <Label htmlFor="molecularWeight">Molecular Weight</Label>
              <Input 
                id="molecularWeight" 
                placeholder="e.g., 28.014 g/mol"
                value={details.molecularWeight} 
                onChange={(e) => handleChange('molecularWeight', e.target.value)} 
              />
            </div>

            <div>
              <Label htmlFor="appearance">Appearance</Label>
              <Input 
                id="appearance" 
                placeholder="e.g., Colorless gas"
                value={details.appearance} 
                onChange={(e) => handleChange('appearance', e.target.value)} 
              />
            </div>

            <div>
              <Label htmlFor="odor">Odor</Label>
              <Input 
                id="odor" 
                placeholder="e.g., Odorless"
                value={details.odor} 
                onChange={(e) => handleChange('odor', e.target.value)} 
              />
            </div>
          </div>

          <div>
            <Label htmlFor="recommendedUse">Recommended Use</Label>
            <Input 
              id="recommendedUse" 
              placeholder="e.g., Industrial gas, Laboratory use"
              value={details.recommendedUse} 
              onChange={(e) => handleChange('recommendedUse', e.target.value)} 
            />
          </div>
        </div>

        {/* Supplier Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold border-b pb-2">Supplier Information</h3>
          
          <div>
            <Label htmlFor="supplierName">Company Name</Label>
            <Input 
              id="supplierName" 
              placeholder="Your company name"
              value={details.supplierName} 
              onChange={(e) => handleChange('supplierName', e.target.value)} 
            />
          </div>

          <div>
            <Label htmlFor="supplierAddress">Address</Label>
            <Input 
              id="supplierAddress" 
              placeholder="Complete company address"
              value={details.supplierAddress} 
              onChange={(e) => handleChange('supplierAddress', e.target.value)} 
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="supplierPhone">Phone Number</Label>
              <Input 
                id="supplierPhone" 
                placeholder="e.g., +60 3-1234 5678"
                value={details.supplierPhone} 
                onChange={(e) => handleChange('supplierPhone', e.target.value)} 
              />
            </div>

            <div>
              <Label htmlFor="emergencyPhone">Emergency Phone</Label>
              <Input 
                id="emergencyPhone" 
                placeholder="24-hour emergency contact"
                value={details.emergencyPhone} 
                onChange={(e) => handleChange('emergencyPhone', e.target.value)} 
              />
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex gap-2 pt-4">
          <Button 
            variant="outline" 
            onClick={handleClear}
            className="flex-1"
          >
            Clear Form
          </Button>
          <div className="flex-1 text-center">
            {isFormValid ? (
              <p className="text-sm text-green-600 font-medium py-2">
                ✓ Ready to generate SDS
              </p>
            ) : (
              <p className="text-sm text-muted-foreground py-2">
                Fill required fields (*) to generate SDS
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
