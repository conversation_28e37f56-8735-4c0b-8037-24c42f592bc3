// components/SDSPDFTemplate.tsx
"use client";

import { GeneratedSDS, ProductDetails } from '@/types/sds';
import { forwardRef } from 'react';

interface SDSPDFTemplateProps {
  content: GeneratedSDS;
  productDetails: ProductDetails;
}

export const SDSPDFTemplate = forwardRef<HTMLDivElement, SDSPDFTemplateProps>(
  ({ content, productDetails }, ref) => {
    const currentDate = new Date().toLocaleDateString('en-GB');
    
    return (
      <div ref={ref} className="bg-white text-black font-sans text-sm leading-tight">
        {/* Page 1 */}
        <div className="min-h-screen p-8 page-break-after">
          {/* Header */}
          <div className="flex justify-between items-start mb-6">
            <div className="flex-1">
              <div className="text-xs text-gray-600 mb-1">
                Phone: +60 3-XXX XXX XX XX
              </div>
              <div className="text-xs text-gray-600">
                Emergency: +60 3-XXX XXX XX XX
              </div>
            </div>
            <div className="bg-red-600 text-white px-4 py-2 font-bold text-lg">
              AGS
            </div>
          </div>

          {/* Company Name */}
          <div className="text-center mb-6">
            <h1 className="text-xl font-bold mb-2">{productDetails.supplierName || 'ALPHA GAS SOLUTION SDN BHD'}</h1>
            <div className="border-2 border-gray-800 p-4">
              <h2 className="text-lg font-bold mb-2">SAFETY DATA SHEET</h2>
              <h3 className="text-base font-semibold">HYDROGEN COMPRESSED GAS</h3>
            </div>
          </div>

          {/* Section 1: Product and Company Information */}
          <div className="border border-gray-400 mb-4">
            <div className="bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400">
              SECTION 1 - PRODUCT AND COMPANY INFORMATION
            </div>
            <div className="p-3 space-y-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="font-semibold">Product Name:</div>
                  <div>{productDetails.gasName}</div>
                </div>
                <div>
                  <div className="font-semibold">Product Code:</div>
                  <div>{productDetails.gasName} ({productDetails.gasCategory} Gas)</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="font-semibold">Other means of identification:</div>
                  <div>
                    {productDetails.gasName}<br/>
                    {productDetails.gasName} Gas<br/>
                    {productDetails.gasName} ({productDetails.gasCategory})
                  </div>
                </div>
                <div>
                  <div className="font-semibold">Recommended use:</div>
                  <div>{productDetails.recommendedUse || 'Industrial gas'}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Section 1.4: Supplier Details */}
          <div className="border border-gray-400 mb-4">
            <div className="bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400">
              1.4 Details of supplier of the safety data sheet
            </div>
            <div className="p-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="font-semibold">Company Name:</div>
                  <div>{productDetails.supplierName}</div>
                </div>
                <div>
                  <div className="font-semibold">Address:</div>
                  <div>{productDetails.supplierAddress}</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <div className="font-semibold">Contact Number:</div>
                  <div>{productDetails.supplierPhone}</div>
                </div>
                <div>
                  <div className="font-semibold">Emergency Contact Number:</div>
                  <div>{productDetails.emergencyPhone}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Section 1.5: Emergency telephone number */}
          <div className="border border-gray-400 mb-6">
            <div className="bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400">
              1.5 Emergency telephone number
            </div>
            <div className="p-3">
              <div className="font-semibold">CHEMTREC:</div>
              <div>******-527-3887 (24 hours)</div>
            </div>
          </div>

          {/* Section 2: Hazards Identification */}
          <div className="border border-gray-400 mb-4">
            <div className="bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400">
              SECTION 2 - HAZARDS IDENTIFICATION
            </div>
            <div className="p-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="font-semibold">2.1 Classification of the substance or mixture:</div>
                  <div>Gases under pressure: {productDetails.gasCategory} gas</div>
                </div>
                <div>
                  <div className="font-semibold">2.2 Label elements:</div>
                  <div>Signal Word: <span className="font-bold">Danger</span></div>
                </div>
              </div>
            </div>
          </div>

          {/* GHS Pictograms */}
          <div className="flex justify-center mb-6">
            <div className="flex space-x-4">
              {/* Flammable pictogram */}
              <div className="w-16 h-16 border-2 border-red-600 bg-white flex items-center justify-center transform rotate-45">
                <div className="transform -rotate-45">
                  <div className="w-8 h-8 bg-red-600 clip-path-flame"></div>
                </div>
              </div>
              {/* Pressure pictogram */}
              <div className="w-16 h-16 border-2 border-red-600 bg-white flex items-center justify-center transform rotate-45">
                <div className="transform -rotate-45 text-red-600 font-bold text-xs">
                  GAS
                </div>
              </div>
            </div>
          </div>

          {/* Hazard Statements */}
          <div className="border border-gray-400 mb-4">
            <div className="p-3">
              <div className="font-semibold mb-2">Hazard Statement(s):</div>
              <div className="text-sm">
                {productDetails.gasCategory === 'Refrigerated' ? (
                  <>H281: Contains refrigerated gas; may cause cryogenic burns or injury</>
                ) : (
                  <>H280: Contains gas under pressure; may explode if heated</>
                )}
              </div>
              <div className="font-semibold mb-2 mt-3">Precautionary Statement(s):</div>
              <div className="text-sm">
                {productDetails.gasCategory === 'Refrigerated' ? (
                  <>
                    P282: Wear cold insulating gloves/face shield/eye protection<br/>
                    P336 + P315: Thaw frosted parts with lukewarm water. Get immediate medical advice<br/>
                    P403: Store in a well-ventilated place
                  </>
                ) : (
                  <>
                    P410 + P403: Protect from sunlight. Store in a well-ventilated place
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="absolute bottom-8 left-8 right-8 border-t-2 border-gray-800 pt-2">
            <div className="flex justify-between items-center text-xs">
              <div>
                Phone: +60 3-XXX XXX XX XX<br/>
                Emergency: +60 3-XXX XXX XX XX
              </div>
              <div className="bg-red-600 text-white px-2 py-1 font-bold">
                AGS
              </div>
            </div>
          </div>
        </div>

        {/* Page 2 */}
        <div className="min-h-screen p-8 page-break-before">
          {/* Header */}
          <div className="flex justify-between items-start mb-6">
            <div className="flex-1">
              <div className="text-xs text-gray-600 mb-1">
                Phone: +60 3-XXX XXX XX XX
              </div>
              <div className="text-xs text-gray-600">
                Emergency: +60 3-XXX XXX XX XX
              </div>
            </div>
            <div className="bg-red-600 text-white px-4 py-2 font-bold text-lg">
              AGS
            </div>
          </div>

          {/* Section 3: Composition/Information on Ingredients */}
          <div className="border border-gray-400 mb-4">
            <div className="bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400">
              3.0 Composition/Information on Ingredients
            </div>
            <div className="p-3">
              <div className="font-semibold mb-2">Pure Substance:</div>
              <div className="text-sm space-y-1">
                <div>Chemical name: {productDetails.gasName}</div>
                <div>CAS number: {productDetails.casNumber}</div>
                <div>Concentration: &gt; 99.9%</div>
              </div>
            </div>
          </div>

          {/* Section 4: First Aid Measures */}
          <div className="border border-gray-400 mb-4">
            <div className="bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400">
              4.0 First Aid Measures (FIRST AID)
            </div>
            <div className="p-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="font-semibold">EYE:</div>
                  <div>Immediately flush eyes with plenty of water for at least 15 minutes. Get medical attention if irritation persists.</div>
                </div>
                <div>
                  <div className="font-semibold">SKIN:</div>
                  <div>Remove contaminated clothing. Wash skin with soap and water. Get medical attention if irritation persists.</div>
                </div>
                <div>
                  <div className="font-semibold">INHALATION:</div>
                  <div>Remove to fresh air immediately. If breathing is difficult, give oxygen. Get medical attention.</div>
                </div>
                <div>
                  <div className="font-semibold">INGESTION:</div>
                  <div>Not applicable for gases.</div>
                </div>
              </div>
            </div>
          </div>

          {/* Section 5: Fire-fighting Measures */}
          <div className="border border-gray-400 mb-4">
            <div className="bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400">
              5.0 Fire-fighting Measures
            </div>
            <div className="p-3 text-sm">
              <div className="font-semibold">Suitable extinguishing media:</div>
              <div className="mb-2">Use extinguishing media appropriate for surrounding fire.</div>
              <div className="font-semibold">Special fire fighting procedures:</div>
              <div className="mb-2">Cool containers with water spray. Evacuate area if containers are exposed to fire.</div>
              <div className="font-semibold">Unusual fire and explosion hazards:</div>
              <div>Containers may rupture or explode when heated.</div>
            </div>
          </div>

          {/* Section 6: Accidental Release Measures */}
          <div className="border border-gray-400 mb-4">
            <div className="bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400">
              6.0 Accidental Release Measures
            </div>
            <div className="p-3 text-sm">
              <div className="font-semibold">Personal precautions:</div>
              <div className="mb-2">Evacuate area. Ensure adequate ventilation. Wear appropriate protective equipment.</div>
              <div className="font-semibold">Environmental precautions:</div>
              <div className="mb-2">Prevent gas from entering sewers, basements, or confined areas.</div>
              <div className="font-semibold">Methods for containment:</div>
              <div>Stop leak if safe to do so. Allow gas to dissipate in well-ventilated area.</div>
            </div>
          </div>

          {/* Section 7: Handling and Storage */}
          <div className="border border-gray-400 mb-4">
            <div className="bg-gray-200 px-3 py-1 font-bold text-sm border-b border-gray-400">
              7.0 Handling and Storage
            </div>
            <div className="p-3 text-sm">
              <div className="font-semibold">Handling:</div>
              <div className="mb-2">Use only in well-ventilated areas. Avoid breathing gas. Use appropriate personal protective equipment.</div>
              <div className="font-semibold">Storage:</div>
              <div>Store in cool, dry, well-ventilated area. Protect from physical damage. Store upright and secure.</div>
            </div>
          </div>
          
          {/* Footer */}
          <div className="absolute bottom-8 left-8 right-8 border-t-2 border-gray-800 pt-2">
            <div className="flex justify-between items-center text-xs">
              <div>
                Phone: +60 3-XXX XXX XX XX<br/>
                Emergency: +60 3-XXX XXX XX XX
              </div>
              <div className="bg-red-600 text-white px-2 py-1 font-bold">
                AGS
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

SDSPDFTemplate.displayName = 'SDSPDFTemplate';
