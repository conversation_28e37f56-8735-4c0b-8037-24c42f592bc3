// app/page.tsx
"use client";

import { useState } from 'react';
import { SDSForm } from '@/components/SDSForm';
import { SDSPreview } from '@/components/SDSPreview';
import { ProductDetails, GeneratedSDS } from '@/types/sds';
import { generateSDSContent } from '@/lib/sdsGenerator';

export default function HomePage() {
  const [sdsData, setSdsData] = useState<GeneratedSDS | null>(null);
  const [currentGasName, setCurrentGasName] = useState<string>('');
  const [currentProductDetails, setCurrentProductDetails] = useState<ProductDetails | null>(null);

  const handleDataChange = (data: ProductDetails) => {
    // Only generate if we have the minimum required data
    if (data.gasName && data.gasCategory && data.casNumber && data.unNumber) {
      const generatedContent = generateSDSContent(data);
      setSdsData(generatedContent);
      setCurrentGasName(data.gasName);
      setCurrentProductDetails(data);
    } else {
      setSdsData(null);
      setCurrentGasName('');
      setCurrentProductDetails(null);
    }
  };

  return (
    <main className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50">
      <div className="container mx-auto p-4 lg:p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            SDS Generator
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Generate professional Safety Data Sheets for compressed gases following ICOP 2014 guidelines.
            Simply fill in the product details and get a complete bilingual SDS instantly.
          </p>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 max-w-7xl mx-auto">
          {/* Form Section */}
          <div className="order-2 xl:order-1">
            <SDSForm onDataChange={handleDataChange} />
          </div>

          {/* Preview Section */}
          <div className="order-1 xl:order-2">
            <SDSPreview
              content={sdsData}
              gasName={currentGasName}
              productDetails={currentProductDetails}
            />
          </div>
        </div>

        {/* Footer */}
        <footer className="mt-16 text-center text-sm text-gray-500">
          <p>
            Built with Next.js, TypeScript, and shadcn/ui • Following ICOP 2014 Guidelines
          </p>
          <p className="mt-1">
            © {new Date().getFullYear()} SDS Generator Tool
          </p>
        </footer>
      </div>
    </main>
  );
}
