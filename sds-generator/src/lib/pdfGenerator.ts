// lib/pdfGenerator.ts
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

export interface PDFGenerationOptions {
  filename?: string;
  quality?: number;
  format?: 'a4' | 'letter';
  orientation?: 'portrait' | 'landscape';
}

export async function generatePDFFromElement(
  element: HTMLElement,
  options: PDFGenerationOptions = {}
): Promise<void> {
  const {
    filename = 'SDS_Document.pdf',
    quality = 1.0,
    format = 'a4',
    orientation = 'portrait'
  } = options;

  try {
    // Configure html2canvas options for better quality
    const canvas = await html2canvas(element, {
      scale: 2, // Higher scale for better quality
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      logging: false,
      width: element.scrollWidth,
      height: element.scrollHeight,
    });

    const imgData = canvas.toDataURL('image/png', quality);
    
    // Calculate PDF dimensions
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;
    
    // A4 dimensions in mm
    const pdfWidth = format === 'a4' ? 210 : 216; // A4 vs Letter
    const pdfHeight = format === 'a4' ? 297 : 279;
    
    // Calculate scaling to fit content
    const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
    const scaledWidth = imgWidth * ratio;
    const scaledHeight = imgHeight * ratio;

    // Create PDF
    const pdf = new jsPDF({
      orientation,
      unit: 'mm',
      format: format === 'a4' ? 'a4' : 'letter'
    });

    // Add image to PDF
    pdf.addImage(
      imgData,
      'PNG',
      0,
      0,
      scaledWidth,
      scaledHeight,
      undefined,
      'FAST'
    );

    // If content is longer than one page, add additional pages
    if (scaledHeight > pdfHeight) {
      let remainingHeight = scaledHeight - pdfHeight;
      let currentY = -pdfHeight;

      while (remainingHeight > 0) {
        pdf.addPage();
        pdf.addImage(
          imgData,
          'PNG',
          0,
          currentY,
          scaledWidth,
          scaledHeight,
          undefined,
          'FAST'
        );
        currentY -= pdfHeight;
        remainingHeight -= pdfHeight;
      }
    }

    // Save the PDF
    pdf.save(filename);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF. Please try again.');
  }
}

export async function generateSDSPDF(
  element: HTMLElement,
  gasName: string,
  companyName?: string
): Promise<void> {
  const timestamp = new Date().toISOString().split('T')[0];
  const sanitizedGasName = gasName.replace(/[^a-zA-Z0-9]/g, '_');
  const sanitizedCompanyName = companyName ? companyName.replace(/[^a-zA-Z0-9]/g, '_') : 'Company';
  
  const filename = `SDS_${sanitizedGasName}_${sanitizedCompanyName}_${timestamp}.pdf`;
  
  await generatePDFFromElement(element, {
    filename,
    quality: 0.95,
    format: 'a4',
    orientation: 'portrait'
  });
}
