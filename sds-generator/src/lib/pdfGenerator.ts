// lib/pdfGenerator.ts
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

export interface PDFGenerationOptions {
  filename?: string;
  quality?: number;
  format?: 'a4' | 'letter';
  orientation?: 'portrait' | 'landscape';
}

export async function generatePDFFromElement(
  element: HTMLElement,
  options: PDFGenerationOptions = {}
): Promise<void> {
  const {
    filename = 'SDS_Document.pdf',
    quality = 1.0,
    format = 'a4',
    orientation = 'portrait'
  } = options;

  try {
    console.log('Starting PDF generation...');
    console.log('Element:', element);
    console.log('Element dimensions:', element.offsetWidth, element.offsetHeight);

    // Wait a bit to ensure element is fully rendered
    await new Promise(resolve => setTimeout(resolve, 100));

    // Configure html2canvas options for better compatibility
    const canvas = await html2canvas(element, {
      scale: 1.5, // Reduced scale for better compatibility
      useCORS: true,
      allowTaint: false,
      backgroundColor: '#ffffff',
      logging: true, // Enable logging for debugging
      width: element.scrollWidth || 800,
      height: element.scrollHeight || 1200,
      windowWidth: 1200,
      windowHeight: 1600,
      removeContainer: true,
      imageTimeout: 15000,
      onclone: (clonedDoc) => {
        console.log('Document cloned for canvas');
        // Ensure all styles are applied to cloned document
        const clonedElement = clonedDoc.querySelector('[data-pdf-template]');
        if (clonedElement) {
          (clonedElement as HTMLElement).style.position = 'static';
          (clonedElement as HTMLElement).style.left = 'auto';
          (clonedElement as HTMLElement).style.top = 'auto';
          (clonedElement as HTMLElement).style.opacity = '1';
        }
      }
    });

    console.log('Canvas created:', canvas.width, canvas.height);

    const imgData = canvas.toDataURL('image/png', quality);
    console.log('Image data created, length:', imgData.length);

    // Calculate PDF dimensions
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;

    // A4 dimensions in mm
    const pdfWidth = format === 'a4' ? 210 : 216;
    const pdfHeight = format === 'a4' ? 297 : 279;

    // Calculate scaling to fit content properly
    const ratio = Math.min(pdfWidth / (imgWidth * 0.264583), pdfHeight / (imgHeight * 0.264583));
    const scaledWidth = (imgWidth * 0.264583) * ratio;
    const scaledHeight = (imgHeight * 0.264583) * ratio;

    console.log('PDF dimensions:', scaledWidth, scaledHeight);

    // Create PDF
    const pdf = new jsPDF({
      orientation,
      unit: 'mm',
      format: format === 'a4' ? 'a4' : 'letter'
    });

    // Add image to PDF with proper positioning
    const xOffset = (pdfWidth - scaledWidth) / 2;
    const yOffset = 10; // Small top margin

    pdf.addImage(
      imgData,
      'PNG',
      Math.max(0, xOffset),
      yOffset,
      Math.min(scaledWidth, pdfWidth),
      Math.min(scaledHeight, pdfHeight - 20),
      undefined,
      'FAST'
    );

    console.log('PDF created successfully');

    // Save the PDF
    pdf.save(filename);
    console.log('PDF saved:', filename);
  } catch (error) {
    console.error('Detailed PDF generation error:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Alternative direct PDF generation method
export async function generateSDSPDFDirect(
  content: any,
  productDetails: any,
  gasName: string,
  companyName?: string
): Promise<void> {
  const timestamp = new Date().toISOString().split('T')[0];
  const sanitizedGasName = gasName.replace(/[^a-zA-Z0-9]/g, '_');
  const sanitizedCompanyName = companyName ? companyName.replace(/[^a-zA-Z0-9]/g, '_') : 'Company';

  const filename = `SDS_${sanitizedGasName}_${sanitizedCompanyName}_${timestamp}.pdf`;

  try {
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    const pageWidth = 210;
    const pageHeight = 297;
    const margin = 20;
    const contentWidth = pageWidth - (margin * 2);
    let currentY = margin;

    // Helper function to add text with word wrapping
    const addText = (text: string, x: number, y: number, options: any = {}) => {
      const fontSize = options.fontSize || 10;
      const maxWidth = options.maxWidth || contentWidth;
      const lineHeight = options.lineHeight || fontSize * 0.35;

      pdf.setFontSize(fontSize);
      if (options.bold) pdf.setFont('helvetica', 'bold');
      else pdf.setFont('helvetica', 'normal');

      const lines = pdf.splitTextToSize(text, maxWidth);

      lines.forEach((line: string, index: number) => {
        if (y + (index * lineHeight) > pageHeight - margin) {
          pdf.addPage();
          y = margin;
        }
        pdf.text(line, x, y + (index * lineHeight));
      });

      return y + (lines.length * lineHeight);
    };

    // Header
    pdf.setFillColor(220, 53, 69); // Red background
    pdf.rect(pageWidth - 40, 10, 30, 15, 'F');
    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.text('AGS', pageWidth - 32, 20);

    // Reset text color
    pdf.setTextColor(0, 0, 0);

    // Company info
    currentY = addText(`Phone: ${productDetails.supplierPhone || '+60 3-XXX XXX XX'}`, margin, 15, { fontSize: 8 });
    currentY = addText(`Emergency: ${productDetails.emergencyPhone || '+60 3-XXX XXX XX'}`, margin, currentY + 2, { fontSize: 8 });

    currentY += 10;

    // Title
    currentY = addText(productDetails.supplierName || 'ALPHA GAS SOLUTION SDN BHD', margin, currentY, {
      fontSize: 16,
      bold: true
    });

    currentY += 5;

    // Draw border around title section
    pdf.rect(margin, currentY, contentWidth, 20);
    currentY = addText('SAFETY DATA SHEET', margin + 5, currentY + 8, {
      fontSize: 14,
      bold: true
    });
    currentY = addText(`${gasName.toUpperCase()} COMPRESSED GAS`, margin + 5, currentY + 3, {
      fontSize: 12,
      bold: true
    });

    currentY += 15;

    // Section 1
    pdf.setFillColor(240, 240, 240);
    pdf.rect(margin, currentY, contentWidth, 8, 'F');
    pdf.rect(margin, currentY, contentWidth, 8);
    currentY = addText('SECTION 1 - PRODUCT AND COMPANY INFORMATION', margin + 2, currentY + 5, {
      fontSize: 10,
      bold: true
    });

    currentY += 5;
    currentY = addText(`Product Name: ${productDetails.gasName}`, margin + 5, currentY, { fontSize: 9 });
    currentY = addText(`CAS Number: ${productDetails.casNumber}`, margin + 5, currentY + 2, { fontSize: 9 });
    currentY = addText(`UN Number: ${productDetails.unNumber}`, margin + 5, currentY + 2, { fontSize: 9 });
    currentY = addText(`Recommended Use: ${productDetails.recommendedUse || 'Industrial gas'}`, margin + 5, currentY + 2, { fontSize: 9 });

    currentY += 8;

    // Section 2
    pdf.setFillColor(240, 240, 240);
    pdf.rect(margin, currentY, contentWidth, 8, 'F');
    pdf.rect(margin, currentY, contentWidth, 8);
    currentY = addText('SECTION 2 - HAZARDS IDENTIFICATION', margin + 2, currentY + 5, {
      fontSize: 10,
      bold: true
    });

    currentY += 5;
    const hazardText = productDetails.gasCategory === 'Refrigerated'
      ? 'H281: Contains refrigerated gas; may cause cryogenic burns or injury'
      : 'H280: Contains gas under pressure; may explode if heated';

    currentY = addText(`Classification: Gases under pressure - ${productDetails.gasCategory} gas`, margin + 5, currentY, { fontSize: 9 });
    currentY = addText('Signal Word: Warning', margin + 5, currentY + 2, { fontSize: 9 });
    currentY = addText(`Hazard Statement: ${hazardText}`, margin + 5, currentY + 2, { fontSize: 9 });

    currentY += 8;

    // Section 3
    pdf.setFillColor(240, 240, 240);
    pdf.rect(margin, currentY, contentWidth, 8, 'F');
    pdf.rect(margin, currentY, contentWidth, 8);
    currentY = addText('SECTION 3 - COMPOSITION/INFORMATION ON INGREDIENTS', margin + 2, currentY + 5, {
      fontSize: 10,
      bold: true
    });

    currentY += 5;
    currentY = addText(`Chemical Identity: ${productDetails.gasName}`, margin + 5, currentY, { fontSize: 9 });
    currentY = addText(`CAS Number: ${productDetails.casNumber}`, margin + 5, currentY + 2, { fontSize: 9 });
    currentY = addText('Composition: >99% (typical purity for industrial gases)', margin + 5, currentY + 2, { fontSize: 9 });

    currentY += 8;

    // Section 4
    pdf.setFillColor(240, 240, 240);
    pdf.rect(margin, currentY, contentWidth, 8, 'F');
    pdf.rect(margin, currentY, contentWidth, 8);
    currentY = addText('SECTION 4 - FIRST AID MEASURES', margin + 2, currentY + 5, {
      fontSize: 10,
      bold: true
    });

    currentY += 5;
    currentY = addText('Inhalation: Remove person to fresh air. If breathing is difficult, give oxygen.', margin + 5, currentY, { fontSize: 9 });
    currentY = addText('Skin Contact: In case of frostbite, warm affected area slowly with lukewarm water.', margin + 5, currentY + 2, { fontSize: 9 });
    currentY = addText('Eye Contact: Flush with water for at least 15 minutes. Get medical attention.', margin + 5, currentY + 2, { fontSize: 9 });

    currentY += 8;

    // Add note about bilingual content
    currentY = addText('Note: This SDS contains both English and Bahasa Malaysia content as required by ICOP 2014.', margin, currentY, {
      fontSize: 8,
      maxWidth: contentWidth
    });

    currentY += 5;
    currentY = addText('Complete 16-section SDS available in the web application with full bilingual content.', margin, currentY, {
      fontSize: 8,
      maxWidth: contentWidth
    });

    // Footer
    const footerY = pageHeight - 15;
    pdf.line(margin, footerY - 5, pageWidth - margin, footerY - 5);
    pdf.setFontSize(8);
    pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, margin, footerY);
    pdf.text('Following ICOP 2014 Guidelines', pageWidth - margin - 50, footerY);

    // Save the PDF
    pdf.save(filename);
    console.log('Direct PDF generated successfully');
  } catch (error) {
    console.error('Direct PDF generation error:', error);
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function generateSDSPDF(
  element: HTMLElement,
  gasName: string,
  companyName?: string
): Promise<void> {
  const timestamp = new Date().toISOString().split('T')[0];
  const sanitizedGasName = gasName.replace(/[^a-zA-Z0-9]/g, '_');
  const sanitizedCompanyName = companyName ? companyName.replace(/[^a-zA-Z0-9]/g, '_') : 'Company';

  const filename = `SDS_${sanitizedGasName}_${sanitizedCompanyName}_${timestamp}.pdf`;

  try {
    await generatePDFFromElement(element, {
      filename,
      quality: 0.95,
      format: 'a4',
      orientation: 'portrait'
    });
  } catch (error) {
    console.error('HTML-to-PDF failed, trying direct generation...');
    throw error; // Re-throw to let the component handle fallback
  }
}
