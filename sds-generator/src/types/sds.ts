// types/sds.ts

export interface ProductDetails {
  gasName: string;
  casNumber: string;
  unNumber: string;
  // This is key for logic: 'Compressed', 'Liquefied', 'Refrigerated', 'Dissolved'
  gasCategory: 'Compressed' | 'Liquefied' | 'Refrigerated' | 'Dissolved' | '';
  recommendedUse: string;
  supplierName: string;
  supplierAddress: string;
  supplierPhone: string;
  emergencyPhone: string;
  isFlammable?: boolean; // Optional for more complex gases
  isToxic?: boolean; // Optional
  molecularFormula?: string;
  molecularWeight?: string;
  appearance?: string;
  odor?: string;
}

export interface SDSSection {
  en: string;
  my: string;
}

// You can expand this to hold the generated content for all 16 sections
export interface GeneratedSDS {
  section1: SDSSection; // Identification
  section2: SDSSection; // Hazard identification
  section3: SDSSection; // Composition/information on ingredients
  section4: SDSSection; // First aid measures
  section5: SDSSection; // Fire-fighting measures
  section6: SDSSection; // Accidental release measures
  section7: SDSSection; // Handling and storage
  section8: SDSSection; // Exposure controls/personal protection
  section9: SDSSection; // Physical and chemical properties
  section10: SDSSection; // Stability and reactivity
  section11: SDSSection; // Toxicological information
  section12: SDSSection; // Ecological information
  section13: SDSSection; // Disposal considerations
  section14: SDSSection; // Transport information
  section15: SDSSection; // Regulatory information
  section16: SDSSection; // Other information
}

export const GAS_CATEGORIES = [
  { value: 'Compressed', label: 'Compressed Gas' },
  { value: 'Liquefied', label: 'Liquefied Gas' },
  { value: 'Refrigerated', label: 'Refrigerated Liquefied Gas' },
  { value: 'Dissolved', label: 'Dissolved Gas' },
] as const;

export const SECTION_TITLES = {
  section1: { en: 'Identification', my: 'Pengenalan' },
  section2: { en: 'Hazard Identification', my: 'Pengenalan Bahaya' },
  section3: { en: 'Composition/Information on Ingredients', my: 'Komposisi/Maklumat mengenai Ramuan' },
  section4: { en: 'First Aid Measures', my: 'Langkah-langkah Pertolongan Cemas' },
  section5: { en: 'Fire-fighting Measures', my: 'Langkah-langkah Memadam Api' },
  section6: { en: 'Accidental Release Measures', my: 'Langkah-langkah Pelepasan Tidak Sengaja' },
  section7: { en: 'Handling and Storage', my: 'Pengendalian dan Penyimpanan' },
  section8: { en: 'Exposure Controls/Personal Protection', my: 'Kawalan Pendedahan/Perlindungan Peribadi' },
  section9: { en: 'Physical and Chemical Properties', my: 'Sifat-sifat Fizikal dan Kimia' },
  section10: { en: 'Stability and Reactivity', my: 'Kestabilan dan Kereaktifan' },
  section11: { en: 'Toxicological Information', my: 'Maklumat Toksikologi' },
  section12: { en: 'Ecological Information', my: 'Maklumat Ekologi' },
  section13: { en: 'Disposal Considerations', my: 'Pertimbangan Pelupusan' },
  section14: { en: 'Transport Information', my: 'Maklumat Pengangkutan' },
  section15: { en: 'Regulatory Information', my: 'Maklumat Peraturan' },
  section16: { en: 'Other Information', my: 'Maklumat Lain' },
} as const;
